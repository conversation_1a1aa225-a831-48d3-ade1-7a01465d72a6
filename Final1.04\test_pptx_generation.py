#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Тестовый скрипт для проверки генерации PPTX файлов с новой функциональностью ИМТ
"""

import os
import sys
from datetime import datetime

# Добавляем текущую директорию в путь для импорта модулей
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_save_report_function():
    """
    Тестирует функцию save_to_pptx из save_report.py
    """
    try:
        from save_report import save_to_pptx
        
        print("Тестирование функции save_to_pptx из save_report.py:")
        print("=" * 60)
        
        # Тестовые данные пациента
        patient_info = {
            'name': 'Тестовый Пациент',
            'client_name': 'Тестовый Пациент',
            'age': '30',
            'client_age': '30',
            'body_type': '170cm, 75kg',
            'test_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'test_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'gender': 'Мужской'
        }
        
        # Тестовые данные элементов (минимальный набор для тестирования)
        test_data = [
            ['Ca', '1200', 'Норма', 'Кальций'],
            ['Fe', '15', 'Дефицит', 'Железо'],
            ['Zn', '12', 'Норма', 'Цинк']
        ]
        
        # Тестовые рекомендации
        recommendations = ['Утренние рекомендации', 'Дневные рекомендации', 'Вечерние рекомендации']
        
        # Несовместимые элементы
        incompatible = ['Элемент1', 'Элемент2']
        
        # Путь для сохранения тестового файла
        test_file_path = 'test_report_save_report.pptx'
        
        print(f"Создание тестового отчета: {test_file_path}")
        print(f"Данные пациента: {patient_info}")
        
        # Создаем mock объекты для tree и get_element_value
        class MockTree:
            def __init__(self):
                self.items = {
                    'item1': {'values': ['Элемент1', 'Утро: препарат1', 'День: препарат2', 'Вечер: препарат3']},
                    'item2': {'values': ['Элемент2', 'Утро: препарат4', 'День: препарат5', 'Вечер: препарат6']}
                }

            def get_children(self, parent=''):
                return list(self.items.keys())

            def item(self, item_id):
                return self.items.get(item_id, {'values': []})

        def mock_get_element_value(element):
            """Mock функция для получения значений элементов"""
            mock_values = {
                'Ca': '1100',
                'Fe': '14',
                'Zn': '11'
            }
            return mock_values.get(element, '0')

        mock_tree = MockTree()

        # Вызываем функцию сохранения с правильными параметрами
        result = save_to_pptx(
            file_path=test_file_path,
            patient_info=patient_info,
            tree=mock_tree,
            get_element_value=mock_get_element_value
        )
        
        if result:
            print("✓ Функция save_to_pptx выполнена успешно")
            if os.path.exists(test_file_path):
                print(f"✓ Файл {test_file_path} создан успешно")
                file_size = os.path.getsize(test_file_path)
                print(f"  Размер файла: {file_size} байт")
            else:
                print(f"✗ Файл {test_file_path} не найден")
        else:
            print("✗ Функция save_to_pptx вернула False")
            
    except ImportError as e:
        print(f"✗ Ошибка импорта save_report: {e}")
    except Exception as e:
        print(f"✗ Ошибка при тестировании save_report: {e}")
        import traceback
        traceback.print_exc()

def test_script009_function():
    """
    Тестирует функцию save_to_pptx из script009.py (через создание экземпляра класса)
    """
    try:
        print("\nТестирование функции save_to_pptx из script009.py:")
        print("=" * 60)
        
        # Создаем минимальный mock объект для тестирования
        class MockScript009:
            def __init__(self):
                self.patient_info = {
                    'name': 'Тестовый Пациент Script009',
                    'client_name': 'Тестовый Пациент Script009',
                    'age': '25',
                    'client_age': '25',
                    'body': '165cm, 60kg',
                    'body_type': '165cm, 60kg',
                    'test_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'test_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'gender': 'Женский'
                }
                
                # Создаем минимальный mock для tree (TreeView)
                self.tree = MockTree()
            
            def calculate_bmi(self, body_type_str):
                """Копия функции расчета ИМТ"""
                try:
                    if not body_type_str:
                        return "Данные не указаны"
                    
                    import re
                    height_match = re.search(r'(\d+(?:\.\d+)?)\s*cm', body_type_str, re.IGNORECASE)
                    weight_match = re.search(r'(\d+(?:\.\d+)?)\s*kg', body_type_str, re.IGNORECASE)
                    
                    if not height_match or not weight_match:
                        return "Некорректные данные"
                    
                    height_cm = float(height_match.group(1))
                    weight_kg = float(weight_match.group(1))
                    height_m = height_cm / 100
                    bmi = weight_kg / (height_m ** 2)
                    
                    if bmi < 18.5:
                        interpretation = "Недостаточный вес"
                    elif 18.5 <= bmi < 25:
                        interpretation = "Нормальный вес"
                    elif 25 <= bmi < 30:
                        interpretation = "Избыточный вес"
                    else:
                        interpretation = "Ожирение"
                    
                    return f"{bmi:.1f} ({interpretation})"
                    
                except (ValueError, AttributeError) as e:
                    return "Ошибка расчета"
            
            def get_element_value(self, element):
                """Mock функция для получения значений элементов"""
                mock_values = {
                    'Ca': '1100',
                    'Fe': '14',
                    'Zn': '11'
                }
                return mock_values.get(element, '0')
        
        class MockTree:
            def __init__(self):
                self.items = {
                    'item1': {'values': ['Элемент1', 'Утро: препарат1', 'День: препарат2', 'Вечер: препарат3']},
                    'item2': {'values': ['Элемент2', 'Утро: препарат4', 'День: препарат5', 'Вечер: препарат6']}
                }
            
            def get_children(self, parent=''):
                return list(self.items.keys())
            
            def item(self, item_id):
                return self.items.get(item_id, {'values': []})
        
        # Создаем экземпляр mock объекта
        mock_app = MockScript009()
        
        # Тестируем расчет ИМТ
        bmi_result = mock_app.calculate_bmi(mock_app.patient_info['body'])
        print(f"Расчет ИМТ: {bmi_result}")
        
        # Тестируем создание маркеров
        markers = {
            '{{client_name}}': mock_app.patient_info.get('client_name', ''),
            '{{client_age}}': mock_app.patient_info.get('client_age', ''),
            '{{body_type}}': mock_app.patient_info.get('body_type', ''),
            '{{test_date}}': mock_app.patient_info.get('test_date', ''),
            '{{gender}}': mock_app.patient_info.get('gender', ''),
            '{{bmi_index}}': bmi_result
        }
        
        print("Созданные маркеры для script009:")
        for marker, value in markers.items():
            print(f"  {marker}: {value}")
        
        print("✓ Тестирование script009 функциональности завершено успешно")
        
    except Exception as e:
        print(f"✗ Ошибка при тестировании script009: {e}")
        import traceback
        traceback.print_exc()

def check_template_files():
    """
    Проверяет наличие файлов шаблонов
    """
    print("\nПроверка файлов шаблонов:")
    print("=" * 40)
    
    templates = [
        'main_template.pptx',
        'main_template_new.pptx',
        'main_template_backup.pptx',
        'Отчет_витаминно_минерального_баланса_17.pptx'
    ]
    
    for template in templates:
        if os.path.exists(template):
            size = os.path.getsize(template)
            print(f"✓ {template} (размер: {size} байт)")
        else:
            print(f"✗ {template} - не найден")

if __name__ == "__main__":
    check_template_files()
    test_save_report_function()
    test_script009_function()
    
    print("\n" + "=" * 70)
    print("Тестирование завершено!")
    print("=" * 70)
