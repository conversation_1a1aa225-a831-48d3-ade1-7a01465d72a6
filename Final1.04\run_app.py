#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Скрипт для запуска Medical Mind приложения
"""

import os
import socket
from app import app

def get_local_ip():
    """Получить локальный IP адрес"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return '127.0.0.1'

def main():
    """Основная функция запуска приложения"""
    # Настройки сервера
    port = int(os.environ.get('PORT', 8080))
    host = os.environ.get('HOST', '127.0.0.1')
    debug = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    
    # Получаем IP адрес
    ip_address = get_local_ip()
    
    # Выводим информацию о запуске
    print('=' * 60)
    print('Medical Mind - Система медицинских отчетов')
    print('=' * 60)
    print(f'Сервер запускается...')
    print(f'* Локальный адрес: http://localhost:{port}')
    print(f'* Внешний адрес: http://{ip_address}:{port}')
    print(f'* Режим отладки: {"Включен" if debug else "Отключен"}')
    print('Для остановки сервера нажмите CTRL+C')
    print('=' * 60)
    
    try:
        # Запускаем приложение
        app.run(
            host=host,
            port=port,
            debug=debug,
            use_reloader=False,  # Отключаем перезагрузку для избежания проблем
            threaded=True
        )
    except KeyboardInterrupt:
        print('\nСервер остановлен пользователем')
    except Exception as e:
        print(f'Ошибка при запуске сервера: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
