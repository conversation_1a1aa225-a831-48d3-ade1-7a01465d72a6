import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import List, Dict, Optional
import codecs
from bs4 import BeautifulSoup
import re
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image as RLImage, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.units import inch, mm
import datetime
import os
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
import pickle
from datetime import datetime
import io
from PIL import Image as PILImage
import requests
from google.oauth2 import service_account
from PIL import Image, ImageDraw, ImageFont
from pptx import Presentation
import time
from save_report import save_report_function
from pptx.util import Pt
from win32com import client

# Определение констант
ELEMENT_FULL_NAMES = {
    "Ca": "Кальций",
    "Fe": "Железо",
    "Zn": "Цинк",
    "Se": "Селен",
    "P": "Фосфор",
    "K(калий)": "Калий",
    "Mg": "Магний",
    "Cu": "Медь",
    "Co": "Кобальт",
    "Mn": "Марганец",
    "I": "Йод",
    "Ni": "Никель",
    "F": "Фтор",
    "Mo": "Молибден",
    "V": "Ванадий",
    "Sn": "Олово",
    "Si": "Кремний",
    "Sr": "Стронций",
    "B (бор)": "Бор",
    "A": "Витамин A",
    "B1": "Витамин B1",
    "B2": "Витамин B2",
    "B3": "Витамин B3",
    "B6": "Витамин B6",
    "B12": "Витамин B12",
    "C": "Витамин C",
    "D3": "Витамин D3",
    "E": "Витамин E",
    "K(витамин)": "Витамин K",
    "NAM": "Никотинамид",
    "CoQ10": "Коэнзим Q10",
    "GSH": "Глутатион",
    "LA": "Липоевая кислота",
    "ALA": "Альфа-липоевая кислота",
    "GLA": "Гамма-линоленовая кислота",
    "AA": "Арахидоновая кислота",
    "Lys": "Лизин",
    "Trp": "Триптофан",
    "Phe": "Фенилаланин",
    "Met": "Метионин",
    "Thr": "Треонин",
    "Ile": "Изолейцин",
    "Leu": "Лейцин",
    "Val": "Валин",
    "His": "Гистидин",
    "Arg": "Аргинин",
    "B7": "Биотин",
    "B5": "Пантотеновая кислота",
    "B9": "Фолиевая кислота",
    "Калий": "K(калий)",
    "Бор": "B (бор)",
    "Витамин K": "K(витамин)"
}

ELEMENT_NAMES_MAP = {
    "Кальций": "Ca",
    "Железо": "Fe",
    "Цинк": "Zn",
    "Селен": "Se",
    "Фосфор": "P",
    "Калий": "K(калий)",
    "Магний": "Mg",
    "Медь": "Cu",
    "Кобальт": "Co",
    "Марганец": "Mn",
    "Йод": "I",
    "Никель": "Ni",
    "Фтор": "F",
    "Молибден": "Mo",
    "Ванадий": "V",
    "Олово": "Sn",
    "Кремний": "Si",
    "Стронций": "Sr",
    "Бор": "B (бор)",
    "Витамин A": "A",
    "Витамин B1": "B1",
    "Витамин B2": "B2",
    "Витамин B3": "B3",
    "Витамин B6": "B6",
    "Витамин B12": "B12",
    "Витамин C": "C",
    "Витамин D3": "D3",
    "Витамин E": "E",
    "Витамин K": "K(витамин)",
    "Никотинамид": "NAM",
    "Коэнзим Q10": "CoQ10",
    "Глутатион": "GSH",
    "Липоевая кислота": "LA",
    "Альфа-липоевая кислота": "ALA",
    "Гамма-линоленовая кислота": "GLA",
    "Арахидоновая кислота": "AA",
    "Лизин": "Lys",
    "Триптофан": "Trp",
    "Фенилаланин": "Phe",
    "Метионин": "Met",
    "Треонин": "Thr",
    "Изолейцин": "Ile",
    "Лейцин": "Leu",
    "Валин": "Val",
    "Гистидин": "His",
    "Аргинин": "Arg",
    "Биотин": "B7",
    "Пантотеновая кислота": "B5",
    "Фолиевая кислота": "B9",
    "Измеряемый параметр": None,
    "витамины A": "A",
    "витамины B1": "B1",
    "витамины B2": "B2",
    "витамины B3": "B3",
    "витамины B6": "B6",
    "витамины B12": "B12",
    "витамины C": "C",
    "витамины D3": "D3",
    "витамины E": "E",
    "витамины K": "K(витамин)",
    "Линолевая кислота": "LA",
    "α-линоленовая кислота": "ALA",
    "γ-линоленовая кислота": "GLA",
    "Система": None,
    "Микроэлементы": None,
    "Витамины (Нутритивный статус)": None,
    "Аминокислоты": None,
    "Коферменты": None,
    "Жирные кислоты": None
}

ELEMENT_REFERENCE_DATA = {
    "Ca": {"morning": "Нет", "day": "Нет", "evening": "Да", "incompatible": "Fe; Zn; Sr"},
    "Fe": {"morning": "Да", "day": "Нет", "evening": "Нет", "incompatible": "B12; Ca; Cu; Ni; V; Sn; B (бор); AA"},
    "Zn": {"morning": "Нет", "day": "Нет", "evening": "Да", "incompatible": "Ca; Cu; Ni; B (бор)"},
    "Se": {"morning": "Да", "day": "Да", "evening": "Нет", "incompatible": "C"},
    "P": {"morning": "Да", "day": "Нет", "evening": "Да", "incompatible": "Mg"},
    "K(калий)": {"morning": "Да", "day": "Да", "evening": "Нет", "incompatible": ""},
    "Mg": {"morning": "Нет", "day": "Нет", "evening": "Да", "incompatible": "P; Sr; B (бор); LA"},
    "Cu": {"morning": "Да", "day": "Нет", "evening": "Нет", "incompatible": "C; B6; Fe; Zn; Mo; Ni"},
    "Co": {"morning": "Да", "day": "Нет", "evening": "Да", "incompatible": ""},
    "Mn": {"morning": "Да", "day": "Да", "evening": "Да", "incompatible": ""},
    "I": {"morning": "Да", "day": "Нет", "evening": "Нет", "incompatible": ""},
    "Ni": {"morning": "Да", "day": "Нет", "evening": "Да", "incompatible": "Cu; Zn; Fe"},
    "F": {"morning": "Нет", "day": "Нет", "evening": "Да", "incompatible": ""},
    "Mo": {"morning": "Нет", "day": "Да", "evening": "Да", "incompatible": "Cu"},
    "V": {"morning": "Нет", "day": "Да", "evening": "Нет", "incompatible": "Fe"},
    "Sn": {"morning": "Нет", "day": "Нет", "evening": "Да", "incompatible": "Fe"},
    "Si": {"morning": "Да", "day": "Да", "evening": "Да", "incompatible": ""},
    "Sr": {"morning": "Нет", "day": "Да", "evening": "Нет", "incompatible": "Ca; Mg"},
    "B (бор)": {"morning": "Да", "day": "Нет", "evening": "Нет", "incompatible": "Mg; Zn; Fe"},
    "A": {"morning": "Да", "day": "Нет", "evening": "Нет", "incompatible": "B12; K(витамин); D3"},
    "B1": {"morning": "Да", "day": "Нет", "evening": "Нет", "incompatible": ""},
    "B2": {"morning": "Да", "day": "Да", "evening": "Нет", "incompatible": "B12; NAM"},
    "B3": {"morning": "Нет", "day": "Да", "evening": "Нет", "incompatible": "C"},
    "B6": {"morning": "Да", "day": "Да", "evening": "Нет", "incompatible": "Cu"},
    "B12": {"morning": "Да", "day": "Нет", "evening": "Да", "incompatible": "C; B2; A; E; Fe"},
    "C": {"morning": "Да", "day": "Да", "evening": "Нет", "incompatible": "B12; B3; Cu; Se"},
    "D3": {"morning": "Да", "day": "Нет", "evening": "Нет", "incompatible": "A"},
    "E": {"morning": "Нет", "day": "Да", "evening": "Да", "incompatible": "B12; K(витамин)"},
    "K(витамин)": {"morning": "Нет", "day": "Нет", "evening": "Да", "incompatible": "A; E"},
    "NAM": {"morning": "Да", "day": "Нет", "evening": "Да", "incompatible": "B2"},
    "CoQ10": {"morning": "Да", "day": "Да", "evening": "Нет", "incompatible": ""},
    "GSH": {"morning": "Нет", "day": "Да", "evening": "Нет", "incompatible": ""},
    "LA": {"morning": "Да", "day": "Нет", "evening": "Да", "incompatible": "Mg"},
    "ALA": {"morning": "Да", "day": "Нет", "evening": "Да", "incompatible": ""},
    "GLA": {"morning": "Да", "day": "Нет", "evening": "Да", "incompatible": ""},
    "AA": {"morning": "Да", "day": "Нет", "evening": "Да", "incompatible": "Fe"},
    "Lys": {"morning": "Да", "day": "Да", "evening": "Нет", "incompatible": ""},
    "Trp": {"morning": "Нет", "day": "Нет", "evening": "Да", "incompatible": ""},
    "Phe": {"morning": "Да", "day": "Да", "evening": "Нет", "incompatible": ""},
    "Met": {"morning": "Да", "day": "Нет", "evening": "Нет", "incompatible": ""},
    "Thr": {"morning": "Да", "day": "Да", "evening": "Нет", "incompatible": ""},
    "Ile": {"morning": "Да", "day": "Да", "evening": "Нет", "incompatible": ""},
    "Leu": {"morning": "Да", "day": "Да", "evening": "Нет", "incompatible": ""},
    "Val": {"morning": "Да", "day": "Да", "evening": "Нет", "incompatible": ""},
    "His": {"morning": "Да", "day": "Да", "evening": "Нет", "incompatible": ""},
    "Arg": {"morning": "Да", "day": "Нет", "evening": "Нет", "incompatible": ""},
    "B7": {"morning": "Нет", "day": "Нет", "evening": "Да", "incompatible": "B5"},
    "B5": {"morning": "Да", "day": "Да", "evening": "Да", "incompatible": "B7"},
    "B9": {"morning": "Да", "day": "Нет", "evening": "Нет", "incompatible": ""}
}

INITIAL_DATA = [
    ["Ca", "1,219 - 3,021", "3,021", "1,009", "незначительные изменения (+)", "-1", "Нет", "Нет", "Да", "Fe; Zn; Sr"],
    ["Fe", "1,151 - 1,847", "1,847", "1,321", "нормально(-)", "0", "Да", "Нет", "Нет", "B12; Ca; Cu; Ni; V; Sn; B (бор); AA"],
    ["Zn", "1,143 - 1,989", "1,989", "1,07", "незначительные изменения (+)", "-1", "Нет", "Нет", "Да", "Ca; Cu; Ni; B (бор)"],
    ["Se", "0,847 - 2,045", "2,045", "1,266", "нормально(-)", "0", "Да", "Да", "Нет", "C"],
    ["P", "1,195 - 2,134", "2,134", "1,144", "незначительные изменения (+)", "-1", "Да", "Нет", "Да", "Mg"],
    ["K(калий)", "0,689 - 0,987", "0,987", "0,584", "незначительные изменения (+)", "-1", "Да", "Да", "Нет", ""],
    ["Mg", "0,568 - 0,992", "0,992", "0,62", "нормально(-)", "0", "Нет", "Нет", "Да", "P; Sr; B (бор); LA"],
    ["Cu", "0,474 - 0,749", "0,749", "0,142", "значительные изменения (++)", "-2", "Да", "Нет", "Нет", "C; B6; Fe; Zn; Mo; Ni"],
    ["Co", "2,326 - 5,531", "5,531", "5,064", "нормально(-)", "0", "Да", "Нет", "Да", ""],
    ["Mn", "0,497 - 0,879", "0,879", "0,504", "нормально(-)", "0", "Да", "Да", "Да", ""],
    ["I", "1,421 - 5,490", "5,490", "3,483", "нормально(-)", "0", "Да", "Нет", "Нет", ""],
    ["Ni", "2,462 - 5,753", "5,753", "3,803", "нормально(-)", "0", "Да", "Нет", "Да", "Cu; Zn; Fe"],
    ["F", "1,954 - 4,543", "4,543", "1,953", "незначительные изменения (+)", "-1", "Нет", "Нет", "Да", ""],
    ["Mo", "0,938 - 1,712", "1,712", "1,43", "нормально(-)", "0", "Нет", "Да", "Да", "Cu"],
    ["V", "1,019 - 3,721", "3,721", "2,739", "нормально(-)", "0", "Нет", "Да", "Нет", "Fe"],
    ["Sn", "1,023 - 7,627", "7,627", "2,538", "нормально(-)", "0", "Нет", "Нет", "Да", "Fe"],
    ["Si", "1,425 - 5,872", "5,872", "1,358", "незначительные изменения (+)", "-1", "Да", "Да", "Да", ""],
    ["Sr", "1,142 - 5,862", "5,862", "2,504", "нормально(-)", "0", "Нет", "Да", "Нет", "Ca; Mg"],
    ["B (бор)", "1,124 - 3,453", "3,453", "3,444", "нормально(-)", "0", "Да", "Нет", "Нет", "Mg; Zn; Fe"],
    ["A", "0,346 - 0,401", "0,401", "0,383", "нормально(-)", "0", "Да", "Нет", "Нет", "B12; K(витамин); D3"],
    ["B1", "2,124 - 4,192", "4,192", "2,759", "нормально(-)", "0", "Да", "Нет", "Нет", ""],
    ["B2", "1,549 - 2,213", "2,213", "1,744", "нормально(-)", "0", "Да", "Да", "Нет", "B12; NAM"],
    ["B3", "14,477 - 21,348", "21,348", "9,942", "значительные изменения (++)", "-2", "Нет", "Да", "Нет", "C"],
    ["B6", "0,824 - 1,942", "1,942", "1,435", "нормально(-)", "0", "Да", "Да", "Нет", "Cu"],
    ["B12", "6,428 - 21,396", "21,396", "10,533", "нормально(-)", "0", "Да", "Нет", "Да", "C; B2; A; E; Fe"],
    ["C", "4,543 - 5,023", "5,023", "4,597", "нормально(-)", "0", "Да", "Да", "Нет", "B12; B3; Cu; Se"],
    ["D3", "5,327 - 7,109", "7,109", "4,173", "значительные изменения (++)", "-2", "Да", "Нет", "Нет", "A"],
    ["E", "4,826 - 6,013", "6,013", "3,896", "значительные изменения (++)", "-2", "Нет", "Да", "Да", "B12; K(витамин)"],
    ["K(витамин)", "0,717 - 1,486", "1,486", "0,513", "значительные изменения (++)", "-2", "Нет", "Нет", "Да", "A; E"],
    ["Lys", "0,253 - 0,659", "0,659", "0,643", "нормально(-)", "0", "Да", "Да", "Нет", ""],
    ["Trp", "2,374 - 3,709", "3,709", "5,263", "значительные изменения (++)", "2", "Нет", "Нет", "Да", ""],
    ["Phe", "0,731 - 1,307", "1,307", "1,245", "нормально(-)", "0", "Да", "Да", "Нет", ""],
    ["Met", "0,432 - 0,826", "0,826", "0,712", "нормально(-)", "0", "Да", "Нет", "Нет", ""],
    ["Thr", "0,422 - 0,817", "0,817", "0,673", "нормально(-)", "0", "Да", "Да", "Нет", ""],
    ["Ile", "1,831 - 3,248", "3,248", "4,131", "незначительные изменения (+)", "1", "Да", "Да", "Нет", ""],
    ["Leu", "2,073 - 4,579", "4,579", "5,05", "незначительные изменения (+)", "1", "Да", "Да", "Нет", ""],
    ["Val", "2,012 - 4,892", "4,892", "5,114", "незначительные изменения (+)", "1", "Да", "Да", "Нет", ""],
    ["His", "2,903 - 4,012", "4,012", "3,956", "нормально(-)", "0", "Да", "Да", "Нет", ""],
    ["Arg", "0,710 - 1,209", "1,209", "1,268", "незначительные изменения (+)", "1", "Да", "Нет", "Нет", ""],
    ["NAM", "2,074 - 3,309", "3,309", "1,919", "незначительные изменения (+)", "-1", "Да", "Нет", "Да", "B2"],
    ["B7", "1,833 - 2,979", "2,979", "1,058", "значительные изменения (++)", "-2", "Нет", "Нет", "Да", "B5"],
    ["B5", "1,116 - 2,101", "2,101", "1,603", "нормально(-)", "0", "Да", "Да", "Да", "B7"],
    ["B9", "1,449 - 2,246", "2,246", "1,543", "нормально(-)", "0", "Да", "Нет", "Нет", ""],
    ["CoQ10", "0,831 - 1,588", "1,588", "0,645", "незначительные изменения (+)", "-1", "Да", "Да", "Нет", ""],
    ["GSH", "0,726 - 1,281", "1,281", "0,933", "нормально(-)", "0", "Нет", "Да", "Нет", ""],
    ["LA", "0,642 - 0,985", "0,985", "0,37", "незначительные изменения (+)", "-1", "Да", "Нет", "Да", "Mg"],
    ["ALA", "0,814 - 1,202", "1,202", "0,454", "значительные изменения (++)", "-2", "Да", "Нет", "Да", ""],
    ["GLA", "0,921 - 1,334", "1,334", "1,081", "нормально(-)", "0", "Да", "Нет", "Да", ""],
    ["AA", "0,661 - 0,808", "0,808", "0,556", "незначительные изменения (+)", "-1", "Да", "Нет", "Да", "Fe"]
]

# Добавьте в начало файла или в подходящее место
element_descriptions = {
    # Минералы
    'Кальций': 'Кальций необходим для поддержания здоровья костей и зубов, Он также участвует в передаче нервных импульсов и сокращении мышц, включая сердечную мышцу.',
    'Железо': 'Железо является основным компонентом гемоглобина, который отвечает за перенос кислорода в крови, Оно также важно для когнитивных функций и иммунитета.',
    'Цинк': 'Цинк поддерживает работу иммунной системы, участвует в выявлении ран и синтезе ДНК, Он также важен для роста и деления клеток. Недостаток цинка может привести к проблемам с иммунитетом.',
    'Селен': 'Селен действует как мощный антиоксидант, защищая органы от повреждений, вызванных свободными радикалами, Он также поддерживает функцию щитовидной железы.',
    'Фосфор': 'Фосфор важен для формирования костей и зубов, а также для производства энергии в клетках, Он участвует в синтезе ДНК и РНК. Недостаток фосфора может привести к слабости и проблемам с костями.',
    'Калий': 'Калий регулирует водно-солевой баланс, поддерживает работу сердца и нервной системы, Он участвует в передаче нервных импульсов и мышечных сокращениях.',
    'Магний': 'Магний необходим для более чем 300 биохимических реакций в организме, Он участвует в синтезе белков, регуляции уровня глюкозы в крови и поддержании здоровья костей.',
    'Медь': 'Медь необходима для производства красных кровяных клеток, поддержания здоровья сосудов и нервной системы, Она также участвует в производстве коллагена.',
    'Кобальт': 'Кобальт является важным компонентом витамина B12, который необходим для синтеза ДНК и производства красных кровяных клеток, Он поддерживает здоровье нервной системы.',
    'Марганец': 'Марганец участвует в метаболизме углеводов, белков и жиров, Он также важен для формирования костей и защиты клеток от окислительного стресса.',
    'Йод': 'Йод необходим для нормальной работы щитовидной железы, которая регулирует обмен веществ, Он также важен для развития мозга и когнитивных функций.',
    'Никель': 'Никель участвует в метаболизме липидов и углеводов, а также может способствовать здоровью клеток, Научные данные о его необходимости в организме ограничены.',
    'Фтор': 'Фтор помогает укрепить зубную эмаль и предотвращает кариес, Он также важен для здоровья костей, Избыток фтора может привести к флюорозу зубов.',
    'Молибден': 'Молибден участвует в метаболизме аминокислот и помогает нейтрализовать токсичные продукты распада, Он важен для поддержания здоровья печени и почек.',
    'Ванадий': 'Ванадий может участвовать в регуляции уровня сахара в крови и метаболизме жиров, Исследования показывают его потенциальное влияние на снижение веса.',
    'Олово': 'Олово оказывает влияние на здоровье щитовидной железы и участвует в некоторых биологических процессах, однако его роль в организме остается неясной.',
    'Кремний': 'Кремний важен для синтеза коллагена и поддержания здоровья кожи, волос и ногтей, Он также способствует укреплению костей и улучшению гибкости суставов.',
    'Стронций': 'Стронций помогает улучшить плотность костей и уменьшить риск переломов, особенно у людей с остеопорозом. Стронций активно используется в медицине.',
    'Бор': 'Бор участвует в метаболизме минералов, таких как кальций, магний и фосфор, Он способствует здоровью костей и улучшает когнитивные функции.',

    # Витамины
    'Витамин A': 'Витамин A важен для здоровья зрения, кожи и иммунной системы, Он способствует росту и восстановлению тканей, поддерживает работу органов.',
    'Витамин B1': 'Витамин B1 (Тиамин) играет ключевую роль в превращении углеводов в энергию и поддерживает работу нервной системы.',
    'Витамин B2': 'Витамин B2 (Рибофлавин) участвует в производстве энергии и поддерживает здоровье кожи, глаз и нервной системы.',
    'Витамин B3': 'Витамин B3 (Ниацин) важен для метаболизма жиров и углеводов, а также для поддержания здоровья кожи и нервной системы.',
    'Витамин B6': 'Витамин B6 (Пиридоксин) необходим для метаболизма белков и углеводов, а также для синтеза нейротрансмиттеров.',
    'Витамин B12': 'Витамин B12 (Кобаламин) важен для синтеза ДНК и красных кровяных клеток, а также для поддержания работы нервной системы.',
    'Витамин C': 'Витамин C (Аскорбиновая кислота) действует как мощный антиоксидант, поддерживая иммунную систему и защищая клетки от окислительного стресса.',
    'Витамин D3': 'Витамин D важен для поддержания здоровья костей, так как помогает организму усваивать кальций. Он также поддерживает иммунную систему.',
    'Витамин E': 'Витамин E является антиоксидантом, который защищает клетки от повреждений, вызванных свободными радикалами.',
    'Витамин K': 'Витамин K важен для свертывания крови и поддержания здоровья костей.',

    # Аминокислоты
    'Лизин': 'Лизин необходим для синтеза белка, помогает в восстановлении мышц и поддерживает иммунную систему.',
    'Триптофан': 'Триптофан является предшественником серотонина и мелатонина, которые важны для регулирования настроения и сна.',
    'Фенилаланин': 'Фенилаланин участвует в синтезе дофамина, который регулирует настроение и мотивацию.',
    'Метионин': 'Метионин важен для синтеза цистеина, который участвует в производстве антиоксидантов.',
    'Треонин': 'Треонин участвует в синтезе коллагена и эластина, которые важны для здоровья кожи и суставов.',
    'Изолейцин': 'Изолейцин является одной из аминокислот с разветвленной цепью (BCAA), которая поддерживает рост и восстановление мышц.',
    'Лейцин': 'Лейцин является ключевой аминокислотой для стимуляции синтеза белка в мышцах и поддержания их роста.',
    'Валин': 'Валин, как и лейцин и изолейцин, является аминокислотой с разветвленной цепью (BCAA), которая поддерживает восстановление мышц.',
    'Гистидин': 'Гистидин необходим для синтеза гемоглобина, который переносит кислород по всему организму.',
    'Аргинин': 'Аргинин участвует в производстве оксида азота, который способствует расширению сосудов и улучшает кровоток.',

    # Коферменты
    'Никотинамид': 'Никотинамид (форма витамина B3) участвует в энергетическом обмене клеток и помогает поддерживать здоровье кожи и нервной системы.',
    'Биотин': 'Витамин B7 (Биотин) важен для метаболизма жиров, белков и углеводов, а также для здоровья волос, кожи и ногтей.',
    'Пантотеновая кислота': 'Витамин B5 (Пантотеновая кислота) необходим для синтеза кофермента А, который участвует в метаболизме жиров.',
    'Фолиевая кислота': 'Витамин B9 (Фолиевая кислота) необходима для синтеза ДНК и клеточного деления.',
    'Коэнзим Q10': 'Коэнзим Q10 играет ключевую роль в производстве энергии в клетках и действует как антиоксидант.',
    'Глутатион': 'Глутатион является основным антиоксидантом в организме, который защищает клетки от окислительных повреждений.',

    # Жирные кислоты
    'Липоевая кислота': 'Липоевая кислота является мощным антиоксидантом, который участвует в метаболизме углеводов и жиров.',
    'Альфа-липоевая кислота': 'Альфа-липоевая кислота (ALA) — это омега-3 жирная кислота, которая действует как антиоксидант и поддерживает регенерацию витаминов C и E.',
    'Гамма-линоленовая кислота': 'Гамма-линоленовая кислота относится к группе омега-6 жирных кислот и важна для здоровья кожи.',
    'Арахидоновая кислота': 'Арахидоновая кислота является омега-6 жирной кислотой, которая участвует в регуляции воспалительных процессов.'
}

class ChangeLevel:
    NORMAL = "нормально(-)"
    MINOR = "незначительные изменения (+)"
    SIGNIFICANT = "значительные изменения (++)"
    SEVERE = "серьезные нарушения (+++)"

class MainTable(ttk.Frame):
    def __init__(self, parent):
        super().__init__(parent)
        self.create_widgets()

    def create_widgets(self):
        # Добавляем столбец 'Описание' в список columns
        columns = ('Название', 'Элемент', 'Норма', 'Max норма', 'Результат', 'Значение', 
                  'Шкала результата', 'Утро', 'День', 'Вечер', 'Несовместимость', 'Описание')
        
        # Создаем контейнер для таблицы и скроллбаров
        table_frame = ttk.Frame(self)
        table_frame.grid(row=0, column=0, sticky='nsew')
        
        # Настраиваем стиль для таблицы
        style = ttk.Style()
        style.configure('Treeview', rowheight=40)
        
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=25)
        
        # Настраиваем заголовки и ширину столбцов
        for col in columns:
            self.tree.heading(col, text=col)
        
        # Добавляем ширину для нового столбца в словарь min_widths
        min_widths = {
            'Название': 150,
            'Элемент': 100,
            'Норма': 120,
            'Max норма': 100,
            'Результат': 100,
            'Значение': 400,
            'Шкала результата': 100,
            'Утро': 80,
            'День': 80,
            'Вечер': 80,
            'Несовместимость': 300,
            'Описание': 400  # Добавляем ширину для столбца описания
        }
        
        for col, width in min_widths.items():
            self.tree.column(col, width=width, minwidth=width)
        
        # Остальной код остается без изменений
        scrollbar_y = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        self.tree.grid(row=0, column=0, sticky='nsew')
        scrollbar_y.grid(row=0, column=1, sticky='ns')
        scrollbar_x.grid(row=1, column=0, sticky='ew')
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)

    def on_double_click(self, event):
        region = self.tree.identify("region", event.x, event.y)
        if region == "cell":
            column = self.tree.identify_column(event.x)
            row = self.tree.identify_row(event.y)
            
            x, y, w, h = self.tree.bbox(row, column)
            
            if column == '#5':  # Значение
                combo = self.value_combo
            elif column in ('#7', '#8', '#9'):  # Утро, День, Вечер
                combo = self.yes_no_combo
            else:
                return
            
            current_value = self.tree.item(row)['values'][int(column[1])-1]
            combo.set(current_value if current_value else '')
            
            combo.place(x=x, y=y, width=w, height=h)
            combo.current_item = (row, column)
            combo.focus()

    def on_combo_selected(self, event):
        combo = event.widget
        if hasattr(combo, 'current_item'):
            row, column = combo.current_item
            col_idx = int(column[1]) - 1
            values = list(self.tree.item(row)['values'])
            values[col_idx] = combo.get()
            
            if column == '#5':
                values[5] = self.calculate_scale(values[4], values[3], values[2])
                self.tree.item(row, values=values)
            combo.place_forget()

    def calculate_scale(self, change_level: str, result: str, max_norm: str) -> str:
        try:
            scale_map = {
                ChangeLevel.NORMAL: 0,
                ChangeLevel.MINOR: 1,
                ChangeLevel.SIGNIFICANT: 2,
                ChangeLevel.SEVERE: 3
            }
            
            scale = scale_map.get(change_level, 0)
            result_value = float(result.replace(',', '.'))
            max_norm_value = float(max_norm.replace(',', '.'))
            
            return str(-scale if result_value < max_norm_value else scale)
        except (ValueError, AttributeError):
            return "0"

    def get_all_data(self) -> List[Dict]:
        data = []
        for item in self.tree.get_children():
            values = self.tree.item(item)['values']
            data.append({
                'name': values[0],
                'element': values[1],
                'norm': values[2],
                'max_norm': values[3],
                'result': values[4],
                'change_level': values[5],
                'scale': values[6],
                'morning': values[7],
                'day': values[8],
                'evening': values[9],
                'incompatible': values[10]
            })
        return data

    def load_data(self, data: List):
        for item in self.tree.get_children():
            self.tree.delete(item)
        for row in data:
            # Получаем полное название элемента из словаря
            element_name = ELEMENT_FULL_NAMES.get(row[0], row[0])
            # Добавляем полное название первым столбцом и описание последним столбцом
            values = [element_name] + row + [element_descriptions.get(element_name, '')]
            self.tree.insert('', 'end', values=values)

    def get_element_value(self, element):
        """Получает значение для элемента из данных таблицы"""
        for item in self.tree.get_children():
            values = self.tree.item(item)['values']
            if values and values[1] == element:  # Предполагаем, что элемент находится во втором столбце
                return values[4]  # Возвращаем значение из колонки "Результат"
        return None  # Если элемент не найден

class ScaleResultTable(ttk.Frame):
    def __init__(self, parent):
        super().__init__(parent)
        self.create_table()
        
    def create_table(self):
        columns = ('Шкала', 'Результат')
        self.tree = ttk.Treeview(self, columns=columns, show='headings', height=7)
        
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=600, stretch=True)
            
        self.tree.grid(row=0, column=0, sticky='nsew')
        
        scrollbar_y = ttk.Scrollbar(self, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(self, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        scrollbar_y.grid(row=0, column=1, sticky='ns')
        scrollbar_x.grid(row=1, column=0, sticky='ew')
        
        # Настраиваем растягивание фрейма
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)

    def update_data(self, scale_data: Dict[int, str]):
        self.tree.delete(*self.tree.get_children())
        for scale in range(3, -4, -1):  # от 3 до -3
            result = scale_data.get(scale, '')
            self.tree.insert('', 'end', values=(scale, result))


class SummaryCompatibilityTable(ttk.Frame):
    def __init__(self, parent):
        super().__init__(parent)
        self.patient_info = {
            'client_name': '',  # Изменено с 'name' на 'client_name'
            'client_age': '',   # Изменено с 'age' на 'client_age'
            'body_type': '',
            'test_date': ''     # Изменено с 'test_time' на 'test_date'
        }  # Инициализируем все ключи
        self.create_table()
        self.create_report_section()

    def create_table(self):
        columns = ('', 'Утро', 'День', 'Вечер')
        
        # Настраиваем стиль для увеличения высоты строк
        style = ttk.Style()
        style.configure('Treeview', rowheight=60)  # Увеличиваем высоту строк для переноса текста
        
        self.tree = ttk.Treeview(self, columns=columns, show='headings', height=3)
        
        for col in columns:
            self.tree.heading(col, text=col)
            if col == '':
                self.tree.column(col, width=200, stretch=True)  # Уменьшаем первую колонку
            else:
                self.tree.column(col, width=400, stretch=True)  # Остальные колонки растягиваются
            
        rows = [
            ('Несовместимые к приему', '', '', ''),
            ('Рекомендации к курсу', '', '', ''),
            ('Несовместимы в курсе', '', '', '')
        ]
        
        for row in rows:
            self.tree.insert('', 'end', values=row)
            
        self.tree.grid(row=0, column=0, sticky='nsew')
        
        scrollbar_y = ttk.Scrollbar(self, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(self, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        scrollbar_y.grid(row=0, column=1, sticky='ns')
        scrollbar_x.grid(row=1, column=0, sticky='ew')

        # Добавляем фрейм для графика
        self.graph_frame = ttk.LabelFrame(self, text="Распределение элементов по времени приема")
        self.graph_frame.grid(row=2, column=0, columnspan=2, sticky='nsew', padx=5, pady=5)
        
        # Настраиваем веса строк и столбцов для правильного растягивания
        self.grid_rowconfigure(2, weight=1)
        self.grid_columnconfigure(0, weight=1)

        # Создаем canvas для графика с фиксированными размерами
        self.canvas = tk.Canvas(self.graph_frame, width=800, height=300, bg='white')
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def update_patient_info(self, info: dict):
        """Обновляет информацию о пациенте"""
        self.patient_info = {
            'client_name': info.get('name', ''),
            'client_age': info.get('age', ''),
            'body_type': info.get('body_type', info.get('body', '')),  # Поддержка обоих вариантов
            'test_date': info.get('test_date', info.get('test_time', ''))  # Поддержка обоих вариантов
        }
        print(f"Обновлена информация о пациенте: {self.patient_info}")  # Для отладки

    def save_to_google_sheets(self):
        """Сохраняет данные в Google Sheets через сервисный аккаунт"""
        try:
            # ID таблицы
            SPREADSHEET_ID = '1v_dRbpRnrEwKOWWmGo8svI3IjdIKJAYmmYEYXNBt4ro'
            
            # Путь к файлу с учетными данными сервисного аккаунта
            CREDENTIALS_FILE = 'service_account.json'
            
            # Область доступа
            SCOPES = ['https://www.googleapis.com/auth/spreadsheets']
            
            # Подготавливаем данные
            current_time = datetime.now().strftime("%d.%m.%Y %H:%M")
            
            try:
                # Авторизуемся с помощью сервисного аккаунта
                credentials = service_account.Credentials.from_service_account_file(
                    CREDENTIALS_FILE, scopes=SCOPES)
                service = build('sheets', 'v4', credentials=credentials)
                
                # Получаем существующие данные
                result = service.spreadsheets().values().get(
                    spreadsheetId=SPREADSHEET_ID,
                    range='A:F'  # Теперь 6 колонок
                ).execute()
                existing_values = result.get('values', [])
                
                # Если таблица пустая, добавляем заголовки
                if not existing_values:
                    headers = [['№', 'Имя клиента', 'Пол', 'Возраст', 'Время тестирования', 'Время сохранения']]
                    service.spreadsheets().values().update(
                        spreadsheetId=SPREADSHEET_ID,
                        range='A1:F1',
                        valueInputOption='RAW',
                        body={'values': headers}
                    ).execute()
                    existing_values = headers
                
                # Проверяем наличие клиента
                client_exists = False
                client_name = self.patient_info.get('client_name', '')
                test_time = self.patient_info.get('test_date', '')
                
                # Получаем последний номер
                last_number = 0
                for row in existing_values[1:]:  # Пропускаем заголовок
                    if len(row) > 0 and row[0].isdigit():
                        last_number = max(last_number, int(row[0]))
                
                # Формируем новую строку с порядковым номером
                new_row = [
                    str(last_number + 1),  # Новый порядковый номер
                    client_name,
                    self.patient_info.get('gender', ''),
                    self.patient_info.get('client_age', ''),
                    test_time,
                    current_time
                ]
                
                # Проверяем на дубликаты
                is_duplicate = False
                for row in existing_values[1:]:  # Пропускаем заголовок
                    if len(row) >= 5 and row[1] == client_name:  # Проверяем имя
                        if row[4] == test_time:  # Проверяем время тестирования
                            is_duplicate = True
                            break
                
                if not is_duplicate:
                    # Добавляем новую строку
                    body = {'values': [new_row]}
                    service.spreadsheets().values().append(
                        spreadsheetId=SPREADSHEET_ID,
                        range='A:F',
                        valueInputOption='RAW',
                        insertDataOption='INSERT_ROWS',
                        body=body
                    ).execute()
                    print("Данные успешно добавлены в Google Sheets")
                else:
                    print(f"Запись для клиента {client_name} с временем тестирования {test_time} уже существует")
                
            except Exception as e:
                print(f"Ошибка при работе с Google Sheets API: {str(e)}")
                raise
            
        except Exception as e:
            print(f"Ошибка при сохранении в Google Sheets: {str(e)}")
            raise
    
    def save_report(self):
        # Создаем диалог сохранения файла
        file_path = filedialog.asksaveasfilename(
            defaultextension=".pptx",
            filetypes=[("PowerPoint files", "*.pptx"), ("PDF files", "*.pdf")],
            title="Сохранить отчет как..."
        )
        
        if not file_path:
            return
            
        if file_path.endswith('.pdf'):
            self.save_to_pdf(file_path)
        else:
            self.save_to_pptx(file_path)

    def calculate_bmi(self, body_type_str):
        """
        Рассчитывает ИМТ из строки телосложения в формате "150cm, 57kg"
        Возвращает строку с ИМТ и интерпретацией
        """
        try:
            if not body_type_str:
                return "Данные не указаны"

            # Извлекаем рост и вес из строки
            import re
            height_match = re.search(r'(\d+(?:\.\d+)?)\s*cm', body_type_str, re.IGNORECASE)
            weight_match = re.search(r'(\d+(?:\.\d+)?)\s*kg', body_type_str, re.IGNORECASE)

            if not height_match or not weight_match:
                return "Некорректные данные"

            height_cm = float(height_match.group(1))
            weight_kg = float(weight_match.group(1))

            # Переводим рост в метры
            height_m = height_cm / 100

            # Рассчитываем ИМТ
            bmi = weight_kg / (height_m ** 2)

            # Определяем интерпретацию ИМТ
            if bmi < 18.5:
                interpretation = "Недостаточный вес"
            elif 18.5 <= bmi < 25:
                interpretation = "Нормальный вес"
            elif 25 <= bmi < 30:
                interpretation = "Избыточный вес"
            else:
                interpretation = "Ожирение"

            return f"{bmi:.1f} ({interpretation})"

        except (ValueError, AttributeError) as e:
            print(f"Ошибка при расчете ИМТ: {e}")
            return "Ошибка расчета"

    def save_to_pptx(self, file_path):
        try:
            # Проверяем наличие всех обязательных данных
            required_fields = ['name', 'age', 'body', 'test_time']
            if not all(field in self.patient_info for field in required_fields):
                messagebox.showerror("Ошибка", "Не хватает данных о пациенте")
                return

            print(f"Сохранение отчета для пациента: {self.patient_info}")

            # Рассчитываем ИМТ
            bmi_value = self.calculate_bmi(self.patient_info.get('body', ''))
            
            # Открываем шаблон PowerPoint
            template_path = "main_template_new.pptx"
            if not os.path.exists(template_path):
                # Если новый шаблон не найден, используем старый
                template_path = "Отчет_витаминно_минерального_баланса_17.pptx"
            
            # Проверяем существование директории и создаем её при необходимости
            save_dir = os.path.dirname(file_path)
            if save_dir and not os.path.exists(save_dir):
                os.makedirs(save_dir)
                
            # Сначала копируем шаблон во временный файл
            temp_path = os.path.join(save_dir, "temp_presentation.pptx")
            
            if os.path.exists(template_path):
                import shutil
                shutil.copy2(template_path, temp_path)
                try:
                    prs = Presentation(temp_path)
                except Exception as e:
                    print(f"Ошибка при открытии временного файла: {str(e)}")
                    prs = Presentation()
                    slide_layout = prs.slide_layouts[0]
                    prs.slides.add_slide(slide_layout)
            else:
                prs = Presentation()
                slide_layout = prs.slide_layouts[0]
                prs.slides.add_slide(slide_layout)
                
            # Получаем данные из таблицы
            items = self.tree.get_children()
            if not items or len(items) < 2:
                raise Exception("Нет данных для сохранения")

            # Заполняем презентацию данными
            for slide in prs.slides:
                for shape in slide.shapes:
                    try:
                        # Заполняем текстовые поля
                        if shape.has_text_frame:
                            text_frame = shape.text_frame
                            text = text_frame.text.strip()
                            
                            # Заполняем информацию о пациенте
                            if "ФИО:" in text:
                                text_frame.paragraphs[0].text = f"ФИО: {self.patient_info['client_name'].strip()}"
                            elif "Возраст:" in text:
                                text_frame.paragraphs[0].text = f"Возраст: {self.patient_info['client_age'].strip()}"
                            elif "Телосложение:" in text:
                                text_frame.paragraphs[0].text = f"Телосложение: {self.patient_info['body_type'].strip()}"
                            elif "Дата и время:" in text:
                                text_frame.paragraphs[0].text = f"Дата и время: {self.patient_info['test_date'].strip()}"
                            elif "Пол:" in text:
                                text_frame.paragraphs[0].text = f"Пол: {self.patient_info.get('gender', '')}"

                            # Обработка маркеров
                            markers = {
                                '{{client_name}}': self.patient_info.get('client_name', ''),
                                '{{client_age}}': self.patient_info.get('client_age', ''),
                                '{{body_type}}': self.patient_info.get('body_type', ''),
                                '{{test_date}}': self.patient_info.get('test_date', ''),
                                '{{gender}}': self.patient_info.get('gender', ''),
                                '{{bmi_index}}': bmi_value
                            }

                            # Заменяем маркеры в тексте
                            original_text = text
                            for marker, value in markers.items():
                                if marker in text:
                                    text = text.replace(marker, str(value))

                            # Если текст изменился, обновляем его
                            if text != original_text:
                                text_frame.paragraphs[0].text = text

                        # Заполняем таблицы
                        if shape.has_table:
                            table = shape.table
                            if len(table.rows) > 0:  # Проверяем, что таблица не пустая
                                header_text = " ".join(cell.text.strip() for cell in table.rows[0].cells)
                                
                                # Таблица с микроэлементами
                                if "Микроэлементы" in header_text:
                                    elements = ["Ca", "Fe", "Zn", "Se", "P", "K(калий)", "Mg", "Cu", "Co", "Mn", "I", "Ni", "F", "Mo", "V", "Sn", "Si", "Sr", "B (бор)"]
                                    if len(table.rows) > 1:  # Проверяем наличие строки для значений
                                        value_row = table.rows[1]
                                        for col_idx, element in enumerate(elements):
                                            if col_idx < len(value_row.cells):
                                                value = self.get_element_value(element)
                                                if value:
                                                    value_row.cells[col_idx].text = str(value)
                            
                                # Таблица рекомендаций
                                elif any(x in header_text for x in ["УТРО", "ДЕНЬ", "ВЕЧЕР"]):
                                    recommendations = self.tree.item(items[1])['values']
                                    if recommendations and len(table.rows) > 1:
                                        for cell in table.rows[1].cells:
                                            cell_text = cell.text.strip().upper()
                                            if "УТРО" in cell_text and len(recommendations) > 1:
                                                cell.text = f"УТРО:\n{str(recommendations[1])}"
                                            elif "ДЕНЬ" in cell_text and len(recommendations) > 2:
                                                cell.text = f"ДЕНЬ:\n{str(recommendations[2])}"
                                            elif "ВЕЧЕР" in cell_text and len(recommendations) > 3:
                                                cell.text = f"ВЕЧЕР:\n{str(recommendations[3])}"
                            
                                # Таблица несовместимостей
                                elif "НЕСОВМЕСТИМЫЕ" in header_text:
                                    incompatible = self.tree.item(items[0])['values']
                                    if incompatible and len(table.rows) > 1:
                                        row = table.rows[1]
                                        for col_idx in range(len(row.cells)):
                                            if col_idx + 1 < len(incompatible):
                                                value = incompatible[col_idx + 1]
                                                if value:
                                                    row.cells[col_idx].text = str(value)
                
                    except Exception as e:
                        print(f"Ошибка при обработке элемента слайда: {str(e)}")
                        continue

            # Сохраняем презентацию
            try:
                prs.save(file_path)
                messagebox.showinfo("Успех", "Отчет успешно сохранен")
            except Exception as e:
                print(f"Ошибка при сохранении презентации: {str(e)}")
                raise
            finally:
                # Удаляем временный файл
                if os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                    except:
                        pass
            
        except Exception as e:
            error_msg = str(e)
            messagebox.showerror("Ошибка", f"Не удалось сохранить отчет: {error_msg}")
            print(f"Подробности ошибки: {str(e)}")

    def get_element_value(self, element):
        """Получает значение для элемента из данных таблицы"""
        try:
            # Получаем данные из главной таблицы (первая вкладка)
            if hasattr(self.master, 'notebook'):
                # Получаем все вкладки
                tabs = self.master.notebook.tabs()
                # Ищем первую вкладку (главную таблицу)
                first_tab = self.master.notebook.select(0)  # Выбираем первую вкладку
                main_frame = self.master.notebook.children[first_tab.split('.')[-1]]
                if hasattr(main_frame, 'tree'):
                    main_tree = main_frame.tree
                    # Ищем элемент в главной таблице
                    for item in main_tree.get_children():
                        values = main_tree.item(item)['values']
                        if values and len(values) > 0:
                            # Проверяем как короткое, так и полное название элемента
                            item_element = str(values[0]).strip()
                            if (item_element == element or 
                                ELEMENT_FULL_NAMES.get(element) == item_element or 
                                ELEMENT_FULL_NAMES.get(item_element) == element):
                                # Возвращаем значение из колонки "Результат"
                                return str(values[1]) if len(values) > 1 else ""

            # Если значение не найдено в главной таблице,
            # проверяем таблицы на вкладке Совместимость
            items = self.tree.get_children()
            if items:
                # Проверяем строку "Несовместимые к приему"
                incompatible = self.tree.item(items[0])['values']
                if incompatible:
                    for value in incompatible[1:]:  # Пропускаем первую колонку
                        if value and isinstance(value, str):
                            elements = [e.strip() for e in str(value).split(';')]
                            if any(e == element or ELEMENT_FULL_NAMES.get(e) == element or 
                                  ELEMENT_FULL_NAMES.get(element) == e for e in elements):
                                return "Несовместим"

                # Проверяем строку "Несовместимы в курсе"
                if len(items) > 2:
                    incompatible_course = self.tree.item(items[2])['values']
                    if incompatible_course:
                        for value in incompatible_course[1:]:  # Пропускаем первую колонку
                            if value and isinstance(value, str):
                                elements = [e.strip() for e in str(value).split(';')]
                                if any(e == element or ELEMENT_FULL_NAMES.get(e) == element or 
                                      ELEMENT_FULL_NAMES.get(element) == e for e in elements):
                                    return "Несовместим в курсе"

            return ""  # Если значение не найдено
            
        except Exception as e:
            print(f"Ошибка при получении значения для элемента {element}: {str(e)}")
            return ""

    def get_table_data(self, table_index):
        """Получает данные из определенной таблицы"""
        table = self.tree.get_children()[table_index]
        data = []
        for item in self.tree.get_children(table):
            values = self.tree.item(item)['values']
            if values:
                data.append(values)
        return data

    def create_report_section(self):
        # Создаем фрейм для отчета
        report_frame = ttk.Frame(self)
        report_frame.grid(row=2, column=0, columnspan=2, sticky='nsew', pady=10)
        
        # Добавляем заголовок для отчета
        header_label = ttk.Label(report_frame, text="Аналитический отчет", font=('Arial', 12, 'bold'))
        header_label.pack(fill='x', padx=5, pady=5)
        
        # Добавляем текстовое поле для краткого отчета с увеличенной высотой
        self.report_text = tk.Text(report_frame, height=15, wrap=tk.WORD, font=('Arial', 10))
        self.report_text.pack(fill='x', padx=5, pady=5)
        
        # Добавляем заголовок для визуализации
        chart_label = ttk.Label(report_frame, text="Распределение элементов по времени приема", font=('Arial', 11, 'bold'))
        chart_label.pack(fill='x', padx=5, pady=5)
        
        # Добавляем канвас для визуализации
        self.canvas = tk.Canvas(report_frame, height=250, bg='white')
        self.canvas.pack(fill='x', padx=5, pady=5)
        
        self.grid_rowconfigure(2, weight=1)

    def update_report(self, morning_count: int, day_count: int, evening_count: int):
        try:
            # Получаем данные из сводной таблицы
            items = self.tree.get_children()
            if not items or len(items) < 3:
                return
                
            row5 = self.tree.item(items[0])['values']  # Несовместимые к приему
            row6 = self.tree.item(items[1])['values']  # Рекомендации к курсу
            row7 = self.tree.item(items[2])['values']  # Несовместимы в курсе
            
            if not all([row5, row6, row7]):
                return
            
            # Получаем элементы для каждого времени приема
            morning_elements = row6[1].split('; ') if row6[1] else []
            day_elements = row6[2].split('; ') if row6[2] else []
            evening_elements = row6[3].split('; ') if row6[3] else []
            
            # Правильно подсчитываем количество элементов
            morning_count = len(morning_elements)
            day_count = len(day_elements)
            evening_count = len(evening_elements)
            total_elements = morning_count + day_count + evening_count
            
            if total_elements == 0:
                return
            
            report = f"Общее количество элементов для приема: {total_elements}\n\n"
            report += f"Распределение по времени приема:\n"
            
            # Добавляем элементы для утреннего приема
            morning_percent = (morning_count/total_elements*100) if total_elements > 0 else 0
            report += f"• Утро: {morning_count} элементов ({morning_percent:.1f}%)\n"
            if morning_elements:
                report += f"  Элементы: {', '.join(morning_elements)}\n"
            
            # Добавляем элементы для дневного приема
            day_percent = (day_count/total_elements*100) if total_elements > 0 else 0
            report += f"• День: {day_count} элементов ({day_percent:.1f}%)\n"
            if day_elements:
                report += f"  Элементы: {', '.join(day_elements)}\n"
            
            # Добавляем элементы для вечернего приема
            evening_percent = (evening_count/total_elements*100) if total_elements > 0 else 0
            report += f"• Вечер: {evening_count} элементов ({evening_percent:.1f}%)\n"
            if evening_elements:
                report += f"  Элементы: {', '.join(evening_elements)}\n"
            
            # Добавляем информацию о несовместимых элементах в курсе
            incompatible_course = row7[1].split('; ') if row7[1] else []
            if incompatible_course:
                report += f"\nНесовместимые элементы в курсе ({len(incompatible_course)} шт.):\n"
                report += f"  {', '.join(incompatible_course)}\n"
            
            report += "\nВажно! Несовместимые элементы:\n"
            # Добавляем несовместимые элементы для утреннего приема
            if row5[1]:
                report += f"• Утром: {row5[1]}\n"
            # Добавляем несовместимые элементы для дневного приема
            if row5[2]:
                report += f"• Днем: {row5[2]}\n"
            # Добавляем несовместимые элементы для вечернего приема
            if row5[3]:
                report += f"• Вечером: {row5[3]}\n"
            
            if hasattr(self, 'report_text'):
                self.report_text.delete('1.0', tk.END)
                self.report_text.insert('1.0', report)
            
            # Обновляем график
            self.update_distribution_graph(morning_count, day_count, evening_count)
            
        except Exception as e:
            print(f"Ошибка при обновлении отчета: {str(e)}")

    def update_distribution_graph(self, morning_count: int, day_count: int, evening_count: int):
        """Обновляет график распределения элементов"""
        try:
            # Очищаем canvas
            self.canvas.delete('all')
            
            # Фиксированные размеры для графика
            width = 800
            height = 300
            
            # Отступы
            padding = 60
            bottom_padding = 50
            
            # Находим максимальное значение для масштабирования
            max_count = max(morning_count, day_count, evening_count, 1)
            
            # Ширина столбца
            bar_width = (width - 2 * padding) / 4
            
            # Масштабный коэффициент для высоты
            scale = (height - padding - bottom_padding - 20) / max_count
            
            # Рисуем оси
            self.canvas.create_line(padding, height - bottom_padding, 
                                  width - padding, height - bottom_padding, 
                                  width=2)  # Ось X
            self.canvas.create_line(padding, height - bottom_padding, 
                                  padding, padding, 
                                  width=2)  # Ось Y
            
            # Функция для рисования столбца
            def draw_bar(x, count, label):
                bar_height = count * scale
                # Рисуем столбец
                self.canvas.create_rectangle(x, height - bottom_padding - bar_height,
                                          x + bar_width - 10, height - bottom_padding,
                                          fill='lightblue', outline='black')
                # Подпись значения
                self.canvas.create_text(x + bar_width/2 - 5, 
                                      height - bottom_padding - bar_height - 15,
                                      text=str(count),
                                      font=('Arial', 10, 'bold'))
                # Подпись категории
                self.canvas.create_text(x + bar_width/2 - 5, 
                                      height - bottom_padding + 15,
                                      text=label,
                                      font=('Arial', 10))
            
            # Рисуем столбцы с увеличенным расстоянием между ними
            draw_bar(padding + bar_width/2, morning_count, "Утро")
            draw_bar(padding + bar_width*2, day_count, "День")
            draw_bar(padding + bar_width*3.5, evening_count, "Вечер")
            
            # Подписи осей
            self.canvas.create_text(width/2, height - 10, 
                                  text="Время приема",
                                  font=('Arial', 10))
            self.canvas.create_text(20, height/2, 
                                  text="Количество элементов", 
                                  angle=90,
                                  font=('Arial', 10))
            
            # Рисуем деления на оси Y
            for i in range(max_count + 1):
                y = height - bottom_padding - i * scale
                self.canvas.create_line(padding - 5, y, padding, y)
                self.canvas.create_text(padding - 15, y, 
                                      text=str(i),
                                      font=('Arial', 8))
            
        except Exception as e:
            print(f"Ошибка при обновлении графика: {str(e)}")

    def update_calculations(self):
        data = self.main_table.get_all_data()
        
        # Update scale result table
        scale_data = self.calculate_scale_results(data)
        self.scale_table.update_data(scale_data)
        
        # Обработка утренней таблицы
        morning_data = self.process_morning_table(data)
        
        # Обновляем утреннюю таблицу
        self.morning_table.update_data(
            compatible=morning_data['compatible'],          
            incompatible=morning_data['morning_no'],       
            calculation_elements=morning_data['calculation_elements'],  
            incompatibility_details=morning_data['morning_yes']        
        )
        
        # Обновляем значения в ячейках [4,1] и [4,2] утренней таблицы
        items_morning = self.morning_table.tree.get_children()
        values_row2_morning = list(self.morning_table.tree.item(items_morning[1])['values'])
        values_row2_morning[1] = morning_data['incompatibilities']     
        values_row2_morning[2] = morning_data['incompatible_morning']  
        self.morning_table.tree.item(items_morning[1], values=values_row2_morning)
        
        # Получаем данные из утренней таблицы для дневной таблицы
        morning_incompatible = morning_data['incompatible_morning']  # [4,2]
        morning_no = morning_data['morning_no']  # [3,2]
        
        # Формируем данные для [5,1] дневной таблицы
        day_calculation_elements = []
        if self.day_table.data_order.get():  # Первый вариант: Несовместимость + время
            if morning_incompatible:
                day_calculation_elements.extend(morning_incompatible.split('; '))
            if morning_no:
                day_calculation_elements.extend(morning_no.split('; '))
        else:  # Второй вариант: Время + несовместимость
            if morning_no:
                day_calculation_elements.extend(morning_no.split('; '))
            if morning_incompatible:
                day_calculation_elements.extend(morning_incompatible.split('; '))
            
        day_calculation_elements = list(filter(None, day_calculation_elements))
        
        # Обработка дневной таблицы
        day_data = self.process_day_table(data, day_calculation_elements)
        
        # Обновляем дневную таблицу
        items_day = self.day_table.tree.get_children()
        
        # Обновляем основные значения
        values_row1_day = list(self.day_table.tree.item(items_day[0])['values'])
        values_row2_day = list(self.day_table.tree.item(items_day[1])['values'])
        values_row3_day = list(self.day_table.tree.item(items_day[2])['values'])
        
        values_row1_day[1] = day_data['compatible']  # [3,1]
        values_row1_day[2] = day_data['day_no']      # [3,2]
        values_row2_day[1] = day_data['incompatibilities']  # [4,1]
        values_row2_day[2] = day_data['incompatible_day']   # [4,2]
        values_row3_day[1] = day_data['calculation_elements']  # [5,1]
        values_row3_day[2] = day_data['day_yes']               # [5,2]
        
        # Обновляем все строки дневной таблицы
        self.day_table.tree.item(items_day[0], values=values_row1_day)
        self.day_table.tree.item(items_day[1], values=values_row2_day)
        self.day_table.tree.item(items_day[2], values=values_row3_day)
        
        # Заполняем ячейку [5,1] вечерней таблицы данными из дневной таблицы
        evening_calculation_elements = []
        if self.evening_table.data_order.get():  # Первый вариант: Несовместимость + время
            # Сначала добавляем данные из ячейки [4,2] дневной таблицы
            if day_data['incompatible_day']:
                evening_calculation_elements.extend(day_data['incompatible_day'].split('; '))
            # Затем добавляем данные из ячейки [3,2] дневной таблицы
            if day_data['day_no']:
                evening_calculation_elements.extend(day_data['day_no'].split('; '))
        else:  # Второй вариант: Время + несовместимость
            # Сначала добавляем данные из ячейки [3,2] дневной таблицы
            if day_data['day_no']:
                evening_calculation_elements.extend(day_data['day_no'].split('; '))
            # Затем добавляем данные из ячейки [4,2] дневной таблицы
            if day_data['incompatible_day']:
                evening_calculation_elements.extend(day_data['incompatible_day'].split('; '))
        
        evening_calculation_elements = list(filter(None, evening_calculation_elements))
        
        # Обрабатываем данные для вечерней таблицы
        evening_data = self.process_evening_table(data, evening_calculation_elements)
        
        # Обновляем вечернюю таблицу
        items_evening = self.evening_table.tree.get_children()
        
        values_row1_evening = list(self.evening_table.tree.item(items_evening[0])['values'])
        values_row2_evening = list(self.evening_table.tree.item(items_evening[1])['values'])
        values_row3_evening = list(self.evening_table.tree.item(items_evening[2])['values'])
        
        values_row1_evening[1] = evening_data['compatible']  # [3,1]
        values_row1_evening[2] = evening_data['evening_no']  # [3,2]
        values_row2_evening[1] = evening_data['incompatibilities']  # [4,1]
        values_row2_evening[2] = evening_data['incompatible_evening']  # [4,2]
        values_row3_evening[1] = evening_data['calculation_elements']  # [5,1]
        values_row3_evening[2] = evening_data['evening_yes']  # [5,2]
        
        # Обновляем все строки вечерней таблицы
        self.evening_table.tree.item(items_evening[0], values=values_row1_evening)
        self.evening_table.tree.item(items_evening[1], values=values_row2_evening)
        self.evening_table.tree.item(items_evening[2], values=values_row3_evening)

        # Заполняем строки 5 и 6 сводной таблицы
        items_summary = self.summary_table.tree.get_children()
        values_row5 = list(self.summary_table.tree.item(items_summary[0])['values'])  # Первая строка (индекс 0) - это строка 5
        values_row6 = list(self.summary_table.tree.item(items_summary[1])['values'])  # Вторая строка (индекс 1) - это строка 6
        values_row7 = list(self.summary_table.tree.item(items_summary[2])['values'])  # Третья строка (индекс 2) - это строка 7
        
        # Получаем данные из ячейки [3,1] каждой таблицы
        morning_compatible = morning_data['compatible']  # [3,1] утренней таблицы
        day_compatible = day_data['compatible']         # [3,1] дневной таблицы
        evening_compatible = evening_data['compatible'] # [3,1] вечерней таблицы
        
        # Заполняем ячейки [6,1], [6,2] и [6,3] сводной таблицы
        values_row6[1] = morning_compatible  # [6,1]
        values_row6[2] = day_compatible      # [6,2]
        values_row6[3] = evening_compatible  # [6,3]
        
        # Получаем несовместимые элементы для элементов из ячейки [6,1]
        incompatible_morning = set()
        if morning_compatible:
            for element in morning_compatible.split('; '):
                for row in data:
                    if row['element'] == element and row['incompatible']:
                        incompatible_morning.update(row['incompatible'].split('; '))
        
        # Получаем несовместимые элементы для элементов из ячейки [6,2]
        incompatible_day = set()
        if day_compatible:
            for element in day_compatible.split('; '):
                for row in data:
                    if row['element'] == element and row['incompatible']:
                        incompatible_day.update(row['incompatible'].split('; '))
        
        # Получаем несовместимые элементы для элементов из ячейки [6,3]
        incompatible_evening = set()
        if evening_compatible:
            for element in evening_compatible.split('; '):
                for row in data:
                    if row['element'] == element and row['incompatible']:
                        incompatible_evening.update(row['incompatible'].split('; '))
        
        # Заполняем ячейки [5,1], [5,2] и [5,3] сводной таблицы
        values_row5[1] = '; '.join(sorted(incompatible_morning))   # [5,1]
        values_row5[2] = '; '.join(sorted(incompatible_day))       # [5,2]
        values_row5[3] = '; '.join(sorted(incompatible_evening))   # [5,3]
        
        # Заполняем ячейку [7,1] данными из вечерней таблицы
        evening_no = evening_data['evening_no']  # [3,2] вечерней таблицы
        evening_incompatible = evening_data['incompatible_evening']  # [4,2] вечерней таблицы
        
        # Объединяем данные из [3,2] и [4,2] вечерней таблицы
        evening_combined = []
        if evening_no:
            evening_combined.extend(evening_no.split('; '))
        if evening_incompatible:
            evening_combined.extend(evening_incompatible.split('; '))
        
        # Записываем в [7,1]
        values_row7[1] = '; '.join(sorted(set(evening_combined)))
        
        # Проверяем каждый элемент из [7,1] на повтор с [5,1]
        elements_71 = values_row7[1].split('; ') if values_row7[1] else []
        elements_51 = values_row5[1].split('; ') if values_row5[1] else []
        elements_61 = values_row6[1].split('; ') if values_row6[1] else []
        
        # Создаем список элементов, которые останутся в [7,1]
        remaining_71_elements = []
        
        for element in elements_71:
            if element and element not in elements_51:  # Если элемента нет в [5,1]
                # Добавляем элемент к списку в [6,1]
                elements_61.append(element)
                
                # Получаем несовместимости для этого элемента
                for row in data:
                    if row['element'] == element and row['incompatible']:
                        # Добавляем несовместимости к списку в [5,1]
                        elements_51.extend(row['incompatible'].split('; '))
            else:
                # Если элемент есть в [5,1], оставляем его в [7,1]
                remaining_71_elements.append(element)
        
        # Обновляем значения в ячейках
        values_row5[1] = '; '.join(sorted(set(elements_51)))
        values_row6[1] = '; '.join(sorted(set(elements_61)))
        values_row7[1] = '; '.join(sorted(set(remaining_71_elements)))
        
        # Проверяем каждый элемент из [7,1] на повтор с [5,2]
        elements_71 = values_row7[1].split('; ') if values_row7[1] else []
        elements_52 = values_row5[2].split('; ') if values_row5[2] else []
        elements_62 = values_row6[2].split('; ') if values_row6[2] else []
        
        # Создаем список элементов, которые останутся в [7,1]
        remaining_71_elements = []
        
        for element in elements_71:
            if element and element not in elements_52:  # Если элемента нет в [5,2]
                # Добавляем элемент к списку в [6,2]
                elements_62.append(element)
                
                # Получаем несовместимости для этого элемента
                for row in data:
                    if row['element'] == element and row['incompatible']:
                        # Добавляем несовместимости к списку в [5,2]
                        elements_52.extend(row['incompatible'].split('; '))
            else:
                # Если элемент есть в [5,2], оставляем его в [7,1]
                remaining_71_elements.append(element)
        
        # Обновляем значения в ячейках
        values_row5[2] = '; '.join(sorted(set(elements_52)))
        values_row6[2] = '; '.join(sorted(set(elements_62)))
        values_row7[1] = '; '.join(sorted(set(remaining_71_elements)))
        
        # Проверяем каждый элемент из [7,1] на повтор с [5,3]
        elements_71 = values_row7[1].split('; ') if values_row7[1] else []
        elements_53 = values_row5[3].split('; ') if values_row5[3] else []
        elements_63 = values_row6[3].split('; ') if values_row6[3] else []
        
        # Создаем список элементов, которые останутся в [7,1]
        remaining_71_elements = []
        
        for element in elements_71:
            if element and element not in elements_53:  # Если элемента нет в [5,3]
                # Добавляем элемент к списку в [6,3]
                elements_63.append(element)
                
                # Получаем несовместимости для этого элемента
                for row in data:
                    if row['element'] == element and row['incompatible']:
                        # Добавляем несовместимости к списку в [5,3]
                        elements_53.extend(row['incompatible'].split('; '))
            else:
                # Если элемент есть в [5,3], оставляем его в [7,1]
                remaining_71_elements.append(element)
        
        # Обновляем значения в ячейках
        values_row5[3] = '; '.join(sorted(set(elements_53)))
        values_row6[3] = '; '.join(sorted(set(elements_63)))
        values_row7[1] = '; '.join(sorted(set(remaining_71_elements)))
        
        # Обновляем строки в сводной таблице
        self.summary_table.tree.item(items_summary[0], values=values_row5)  # Обновляем строку 5
        self.summary_table.tree.item(items_summary[1], values=values_row6)  # Обновляем строку 6
        self.summary_table.tree.item(items_summary[2], values=values_row7)  # Обновляем строку 7

        # Подсчитываем количество элементов для каждого времени приема
        morning_count = len(morning_compatible.split('; ')) if morning_compatible else 0
        day_count = len(day_compatible.split('; ')) if day_compatible else 0
        evening_count = len(evening_compatible.split('; ')) if evening_compatible else 0

        # Обновляем отчет и визуализацию
        self.summary_table.update_report(morning_count, day_count, evening_count)
        
        # Обновляем таблицы распределения
        self.distribution_table1.update_values(data)
        self.distribution_table2.update_values(data)
        self.distribution_table3.update_values(data)
        self.distribution_table4.update_values(data)
        self.distribution_table5.update_values(data)

    def wrap_text(self, text: str, max_chars: int = 30) -> str:
        """Форматирует текст с переносами строк"""
        if not text:
            return ''
            
        elements = text.split('; ')
        lines = []
        current_line = []
        current_length = 0
        
        for element in elements:
            if current_length + len(element) + 2 <= max_chars:
                current_line.append(element)
                current_length += len(element) + 2
            else:
                if current_line:
                    lines.append('; '.join(current_line))
                current_line = [element]
                current_length = len(element)
        
        if current_line:
            lines.append('; '.join(current_line))
            
        return '\n'.join(lines)

class CompatibilityTimeTable(ttk.Frame):
    def __init__(self, parent, title: str):
        super().__init__(parent)
        self.title = title
        self.data_order = tk.BooleanVar(value=True)  # True для первого варианта, False для второго
        self.create_table()
        if title in ["днем", "вечером"]:  # Добавляем переключатель для дневной и вечерней таблиц
            self.create_switch()
    
    def create_switch(self):
        switch_frame = ttk.Frame(self)
        switch_frame.grid(row=2, column=0, sticky='w', padx=5, pady=5)
        
        self.switch = ttk.Checkbutton(
            switch_frame,
            text="(Несовместимость + время/ Время + несовместимость)",
            variable=self.data_order,
            command=self.on_switch_change
        )
        self.switch.pack(side='left')
    
    def on_switch_change(self):
        # Этот метод будет вызываться при изменении переключателя
        if hasattr(self, 'parent_calculator'):
            self.parent_calculator.update_calculations()
    
    def create_table(self):
        columns = ('', 'Расчет совместимости', 'Несовместимость')
        self.tree = ttk.Treeview(self, columns=columns, show='headings')
        
        # Настраиваем стиль для переноса текста
        style = ttk.Style()
        style.configure('Treeview', rowheight=40)  # Уменьшаем высоту строк
        
        for col in columns:
            self.tree.heading(col, text=col)
            if col == '':
                self.tree.column(col, width=150, anchor='w', stretch=False)
            else:
                self.tree.column(col, width=400, anchor='w', stretch=False)
            
        rows = [
            (f'К приему {self.title}', '', ''),
            ('Несовместимые в приеме', '', ''),
            ('Элементы для расчета', '', '')
        ]
        
        for row in rows:
            self.tree.insert('', 'end', values=row)
            
        self.tree.grid(row=0, column=0, sticky='nsew', padx=5, pady=5)
        
        # Настраиваем растягивание фрейма
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)

    def update_data(self, compatible: str, incompatible: str, calculation_elements: str, incompatibility_details: str = ''):
        items = self.tree.get_children()
        
        # Функция для форматирования длинного текста
        def format_long_text(text: str, max_chars: int = 50) -> str:
            if not text:
                return ''
            words = text.split('; ')
            lines = []
            current_line = []
            current_length = 0
            
            for word in words:
                if current_length + len(word) + 2 <= max_chars:  # +2 для "; "
                    current_line.append(word)
                    current_length += len(word) + 2
                else:
                    if current_line:
                        lines.append('; '.join(current_line))
                    current_line = [word]
                    current_length = len(word)
            
            if current_line:
                lines.append('; '.join(current_line))
            
            return '\n'.join(lines)
        
        # Обновляем значения с форматированием
        values_row1 = list(self.tree.item(items[0])['values'])
        values_row2 = list(self.tree.item(items[1])['values'])
        values_row3 = list(self.tree.item(items[2])['values'])
        
        values_row1[1] = format_long_text(compatible)
        values_row1[2] = format_long_text(incompatible)
        values_row2[1] = format_long_text(values_row2[1])  # Оставляем текущее значение [4,1]
        values_row2[2] = format_long_text(values_row2[2])  # Оставляем текущее значение [4,2]
        values_row3[1] = format_long_text(calculation_elements)
        values_row3[2] = format_long_text(incompatibility_details)
        
        self.tree.item(items[0], values=values_row1)
        self.tree.item(items[1], values=values_row2)
        self.tree.item(items[2], values=values_row3)

class DistributionTable(ttk.Frame):
    def __init__(self, parent, title: str, columns: List[str]):
        super().__init__(parent)
        self.columns = columns
        self.create_table(title, columns)
        
    def create_table(self, title: str, columns: List[str]):
        self.tree = ttk.Treeview(self, columns=columns, show='headings', height=1)
        
        for i, col in enumerate(columns):
            self.tree.heading(col, text=col)
            if i == 0:
                self.tree.column(col, width=150, stretch=False)
            else:
                self.tree.column(col, width=60, stretch=False)
            
        self.tree.insert('', 'end', values=('Шкала результата',) + ('',) * (len(columns) - 1))
        
        self.tree.grid(row=0, column=0, sticky='nsew')
        
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
    
    def update_values(self, main_table_data: List[Dict]):
        item = self.tree.get_children()[0]
        values = ['Шкала результата']
        
        for col in self.columns[1:]:
            scale_value = ''
            for row in main_table_data:
                if row['element'] == col:
                    scale_value = row['scale']
                    break
            values.append(scale_value)
        
        self.tree.item(item, values=tuple(values))

def get_services():
    """Получение сервисов для работы с Google Sheets и Drive"""
    try:
        SCOPES = ['https://www.googleapis.com/auth/spreadsheets', 'https://www.googleapis.com/auth/drive']
        creds = None
        
        if os.path.exists('token.pickle'):
            with open('token.pickle', 'rb') as token:
                creds = pickle.load(token)
                
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                flow = InstalledAppFlow.from_client_secrets_file(
                    'credentials.json', 
                    SCOPES,
                    redirect_uri='http://localhost:8080'
                )
                creds = flow.run_local_server(port=8080)
                
            with open('token.pickle', 'wb') as token:
                pickle.dump(creds, token)
        
        sheets_service = build('sheets', 'v4', credentials=creds)
        drive_service = build('drive', 'v3', credentials=creds)
        return sheets_service, drive_service
        
    except Exception as e:
        print(f"Ошибка при получении сервисов: {str(e)}")
        raise

def create_new_spreadsheet(sheets_service, drive_service):
    """Создает новую таблицу для отчетов"""
    try:
        spreadsheet = {
            'properties': {
                'title': 'Отчеты по элементам'
            },
            'sheets': [{
                'properties': {
                    'title': 'Отчеты',
                    'gridProperties': {
                        'rowCount': 1000,
                        'columnCount': 5
                    }
                }
            }]
        }
        
        spreadsheet = sheets_service.spreadsheets().create(body=spreadsheet).execute()
        spreadsheet_id = spreadsheet['spreadsheetId']
        
        # Добавляем заголовки
        values = [['Имя клиента', 'Пол', 'Возраст', 'Время тестирования', 'Время сохранения']]
        body = {'values': values}
        
        sheets_service.spreadsheets().values().update(
            spreadsheetId=spreadsheet_id,
            range='Отчеты!A1:E1',
            valueInputOption='RAW',
            body=body
        ).execute()
        
        return spreadsheet_id
        
    except Exception as e:
        print(f"Ошибка при создании таблицы: {str(e)}")
        raise

class CompatibilityCalculator(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Калькулятор совместимости элементов")
        self.geometry("1920x1080")  # Увеличиваем размер окна до Full HD
        self.state('zoomed')  # Открываем окно в развернутом состоянии
        
        # Инициализация информации о пациенте
        self.patient_info = {
            'client_name': '',
            'client_age': '',
            'body_type': '',
            'test_date': '',
            'gender': ''
        }
        self.create_widgets()
        self.load_initial_data()

    def create_widgets(self):
        # Создаем фрейм для кнопок
        button_frame = ttk.Frame(self)
        button_frame.pack(pady=5)

        # Кнопка "Загрузить отчет"
        ttk.Button(button_frame, text="Загрузить отчет", command=self.load_report).pack(side='left', padx=5)
        
        # Кнопка "Сохранить отчет"
        ttk.Button(button_frame, text="Сохранить отчет", command=lambda: (
            self.update_patient_info({}),  # Сначала обновляем информацию о пациенте
            print(f"Перед сохранением отчета patient_info: {self.patient_info}"),  # Для отладки
            save_report_function(self.patient_info, self.main_table.tree, self.main_table.get_element_value)
        )).pack(side='left', padx=5)  # Передаем необходимые аргументы

        # Создаем фрейм для информации о клиенте
        patient_info_frame = ttk.LabelFrame(self, text="Информация о клиенте", padding=10)
        patient_info_frame.pack(fill='x', padx=10, pady=5)
        
        # Создаем метки для информации о клиенте
        self.patient_info_label1 = ttk.Label(patient_info_frame, font=('TkDefaultFont', 10))
        self.patient_info_label1.pack(anchor='w', padx=5, pady=2)
        
        self.patient_info_label2 = ttk.Label(patient_info_frame, font=('TkDefaultFont', 10))
        self.patient_info_label2.pack(anchor='w', padx=5, pady=2)

        self.notebook = ttk.Notebook(self)
        self.notebook.pack(expand=True, fill='both', padx=5, pady=5)
        
        # Создаем фреймы для вкладок
        main_frame = ttk.Frame(self.notebook)
        scale_frame = ttk.Frame(self.notebook)
        compatibility_frame = ttk.Frame(self.notebook)
        summary_frame = ttk.Frame(self.notebook)
        distribution_frame = ttk.Frame(self.notebook)
        
        # Добавляем фреймы в вкладки
        self.notebook.add(main_frame, text='Главная')
        self.notebook.add(scale_frame, text='Шкала')
        self.notebook.add(compatibility_frame, text='Совместимость')
        self.notebook.add(summary_frame, text='Сводная')
        self.notebook.add(distribution_frame, text='Распределение')

        self.main_table = MainTable(main_frame)
        self.main_table.pack(expand=True, fill='both', padx=5, pady=5)
        
        self.scale_table = ScaleResultTable(scale_frame)
        self.scale_table.pack(expand=True, fill='both', padx=5, pady=5)
        
        self.summary_table = SummaryCompatibilityTable(summary_frame)
        self.summary_table.pack(expand=True, fill='both', padx=5, pady=5)
        
        # Создаем контейнер для таблиц совместимости
        tables_frame = ttk.Frame(compatibility_frame)
        tables_frame.pack(expand=True, fill='both', padx=5, pady=5)
        
        # Утренняя таблица
        self.morning_table = CompatibilityTimeTable(tables_frame, "утром")
        self.morning_table.grid(row=0, column=0, sticky='nsew', padx=5, pady=5)
        
        # Дневная таблица
        self.day_table = CompatibilityTimeTable(tables_frame, "днем")
        self.day_table.parent_calculator = self
        self.day_table.grid(row=1, column=0, sticky='nsew', padx=5, pady=5)
        
        # Вечерняя таблица
        self.evening_table = CompatibilityTimeTable(tables_frame, "вечером")
        self.evening_table.parent_calculator = self
        self.evening_table.grid(row=2, column=0, sticky='nsew', padx=5, pady=5)
        
        # Настраиваем растягивание строк в tables_frame
        tables_frame.grid_rowconfigure(0, weight=1)
        tables_frame.grid_rowconfigure(1, weight=1)
        tables_frame.grid_rowconfigure(2, weight=1)
        tables_frame.grid_columnconfigure(0, weight=1)
        
        # Создаем контейнер для таблиц распределения
        distribution_tables_frame = ttk.Frame(distribution_frame)
        distribution_tables_frame.pack(expand=True, fill='both', padx=5, pady=5)
        
        # Определяем колонки для каждой таблицы
        microelements_cols = ['Микроэлементы', 'Ca', 'Fe', 'Zn', 'Se', 'P', 'K(калий)', 'Mg', 'Cu', 'Co', 'Mn', 'I', 'Ni', 'F', 'Mo', 'V', 'Sn', 'Si', 'Sr', 'B (бор)']
        vitamins_cols = ['Витамины', 'A', 'B1', 'B2', 'B3', 'B6', 'B12', 'C', 'D3', 'E', 'K(витамин)']
        amino_acids_cols = ['Аминокислоты', 'Lys', 'Trp', 'Phe', 'Met', 'Thr', 'Ile', 'Leu', 'Val', 'His', 'Arg']
        coenzymes_cols = ['Коферменты', 'NAM', 'B7', 'B5', 'B9', 'CoQ10', 'GSH']
        fatty_acids_cols = ['Жирные кислоты', 'LA', 'ALA', 'GLA', 'AA']
        
        # Создаем таблицы
        self.distribution_table1 = DistributionTable(distribution_tables_frame, "Микроэлементы", microelements_cols)
        self.distribution_table1.grid(row=0, column=0, sticky='nsew', padx=5, pady=5)
        
        self.distribution_table2 = DistributionTable(distribution_tables_frame, "Витамины", vitamins_cols)
        self.distribution_table2.grid(row=1, column=0, sticky='nsew', padx=5, pady=5)
        
        self.distribution_table3 = DistributionTable(distribution_tables_frame, "Аминокислоты", amino_acids_cols)
        self.distribution_table3.grid(row=2, column=0, sticky='nsew', padx=5, pady=5)
        
        self.distribution_table4 = DistributionTable(distribution_tables_frame, "Коферменты", coenzymes_cols)
        self.distribution_table4.grid(row=3, column=0, sticky='nsew', padx=5, pady=5)
        
        self.distribution_table5 = DistributionTable(distribution_tables_frame, "Жирные кислоты", fatty_acids_cols)
        self.distribution_table5.grid(row=4, column=0, sticky='nsew', padx=5, pady=5)
        
        for i in range(5):
            distribution_tables_frame.grid_rowconfigure(i, weight=1)
        distribution_tables_frame.grid_columnconfigure(0, weight=1)

    def load_report(self):
        """Загружает и парсит HTML отчет"""
        file_path = filedialog.askopenfilename(
            filetypes=[("HTML files", "*.htm;*.html")],
            title="Выберите файл отчета"
        )
        
        if not file_path:
            return
            
        try:
            # Сначала читаем файл в бинарном режиме
            with open(file_path, 'rb') as file:
                raw_content = file.read()
            
            # Пробуем определить кодировку из содержимого файла
            encodings = [
                ('windows-1251', 'Кальций'),
                ('utf-8', 'Кальций'),
                ('utf-8-sig', 'Кальций'),
                ('cp1251', 'Кальций'),
                ('windows-1251', 'кальций'),
                ('utf-8', 'кальций'),
                ('utf-8-sig', 'кальций'),
                ('cp1251', 'кальций')
            ]
            
            content = None
            used_encoding = None
            
            for encoding, test_str in encodings:
                try:
                    decoded = raw_content.decode(encoding)
                    if test_str in decoded:
                        content = decoded
                        used_encoding = encoding
                        print(f"Успешно декодировано с использованием {encoding}")
                        break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    print(f"Ошибка при декодировании с {encoding}: {str(e)}")
                    continue
            
            if not content:
                # Если не удалось определить кодировку, пробуем прочитать через codecs
                try:
                    with codecs.open(file_path, 'r', encoding='windows-1251') as file:
                        content = file.read()
                        used_encoding = 'windows-1251 (codecs)'
                        print("Успешно прочитано через codecs")
                except Exception as e:
                    print(f"Ошибка при чтении через codecs: {str(e)}")
                    content = None
            
            if not content:
                raise Exception("Не удалось прочитать файл ни с одной из поддерживаемых кодировок")
            
            print(f"Используемая кодировка: {used_encoding}")
            
            # Создаем объект BeautifulSoup с явным указанием парсера
            soup = BeautifulSoup(content, 'html.parser')
            
            # Извлекаем информацию о клиенте
            try:
                # Находим все td элементы, содержащие нужную информацию
                info_cells = soup.find_all('td')
                patient_info = {}
                
                for cell in info_cells:
                    text = cell.get_text().strip()
                    if 'Имя:' in text:
                        patient_info['name'] = text.split('Имя:')[1].strip()
                    elif 'Пол:' in text:
                        patient_info['gender'] = text.split('Пол:')[1].strip()
                    elif 'Возраст:' in text:
                        patient_info['age'] = text.split('Возраст:')[1].strip()
                    elif 'Телосложение:' in text:
                        patient_info['body'] = text.split('Телосложение:')[1].strip()
                    elif 'Время тестирования:' in text:
                        test_time = text.split('Время тестирования:')[1].strip().replace('\n', ' ')
                        patient_info['test_time'] = test_time

                # Обновляем информацию о клиенте
                name = patient_info.get('name', '')
                gender = patient_info.get('gender', '')
                age = patient_info.get('age', '')
                body = patient_info.get('body', '')
                test_time = patient_info.get('test_time', '')

                # Форматируем первую строку
                self.patient_info_label1.configure(
                    text=f"Имя: {name}     Пол: {gender}     Возраст: {age}     Время тестирования: {test_time}",
                    justify='left'
                )
                
                # Форматируем вторую строку
                self.patient_info_label2.configure(
                    text=f"Телосложение: {body}",
                    justify='left'
                )
                
                # Обновляем информацию в summary_table для сохранения в PDF и Google Sheets
                self.summary_table.update_patient_info(patient_info)

            except Exception as e:
                print(f"Ошибка при извлечении информации о клиенте: {e}")
                self.patient_info_label1.config(text="")
                self.patient_info_label2.config(text="")

            # Парсим данные из таблицы
            parsed_data = []
            
            # Находим все строки таблицы с данными
            rows = soup.find_all('tr')
            not_found_elements = []  # Список для элементов, которых нет в словаре
            
            # Список заголовков, которые нужно игнорировать
            ignore_headers = {
                "Измеряемый параметр", 
                "Система", 
                "Микроэлементы", 
                "Витамины (Нутритивный статус)",
                "Аминокислоты", 
                "Коферменты", 
                "Жирные кислоты",
                "Рекомендации",
                "Имя",
                "Пол",
                "Возраст",
                "Телосложение",
                "Время тестирования"
            }
            
            for row in rows:
                cols = row.find_all('td')
                if len(cols) >= 4:  # Проверяем, что есть хотя бы 4 столбца
                    element_text = cols[0].get_text(strip=True)
                    
                    # Пропускаем заголовки и информацию о пациенте
                    if (element_text in ignore_headers or 
                        not element_text or 
                        "Имя:" in element_text or 
                        "Пол:" in element_text or 
                        "Возраст:" in element_text or 
                        "Телосложение:" in element_text or 
                        "Время тестирования:" in element_text):
                        continue
                    
                    # Получаем данные из столбцов
                    norm_range = cols[1].get_text(strip=True)
                    result = cols[2].get_text(strip=True)
                    interpretation = cols[3].get_text(strip=True)  # Это значение из отчета
                    
                    print(f"Обрабатываем элемент: {element_text}")  # Отладочный вывод
                    
                    # Пробуем найти код элемента
                    element_code = None
                    
                    # Нормализуем текст элемента (убираем лишние пробелы и приводим к нижнему регистру)
                    element_text_normalized = ' '.join(element_text.lower().split())
                    
                    # 1. Прямой поиск в ELEMENT_NAMES_MAP (с учетом регистра)
                    element_code = ELEMENT_NAMES_MAP.get(element_text)
                    
                    # 2. Поиск без учета регистра
                    if not element_code:
                        for name, code in ELEMENT_NAMES_MAP.items():
                            if name.lower() == element_text_normalized:
                                element_code = code
                                break
                    
                    # 3. Поиск по полному названию в ELEMENT_FULL_NAMES
                    if not element_code:
                        for code, name in ELEMENT_FULL_NAMES.items():
                            if name.lower() == element_text_normalized:
                                element_code = code
                                break
                    
                    # 4. Поиск по коду напрямую
                    if not element_code and element_text in ELEMENT_FULL_NAMES:
                        element_code = element_text
                    
                    if element_code:
                        try:
                            # Разбираем диапазон нормы
                            norm_min, norm_max = norm_range.split(' - ')
                            
                            # Получаем справочные данные для элемента
                            ref_data = ELEMENT_REFERENCE_DATA.get(element_code, {
                                "morning": "Нет",
                                "day": "Нет",
                                "evening": "Нет",
                                "incompatible": ""
                            })
                            
                            # Определяем шкалу на основе интерпретации
                            if "нормально" in interpretation.lower():
                                scale = "0"
                            elif "незначительные" in interpretation.lower():
                                scale = "-1" if float(result.replace(',', '.')) < float(norm_max.replace(',', '.')) else "1"
                            elif "значительные" in interpretation.lower():
                                scale = "-2" if float(result.replace(',', '.')) < float(norm_max.replace(',', '.')) else "2"
                            elif "серьезные" in interpretation.lower():
                                scale = "-3" if float(result.replace(',', '.')) < float(norm_max.replace(',', '.')) else "3"
                            else:
                                scale = "0"
                            
                            # Создаем новую строку данных
                            new_row = [
                                element_code,           # Элемент
                                norm_range,            # Норма
                                norm_max,              # Max норма
                                result,                # Результат
                                interpretation.replace('\n', ' ').strip(),  # Значение (берем из отчета, убираем переносы)
                                scale,                 # Шкала результата
                                ref_data["morning"],   # Утро
                                ref_data["day"],       # День
                                ref_data["evening"],   # Вечер
                                ref_data["incompatible"] # Несовместимость
                            ]
                            
                            parsed_data.append(new_row)
                        except ValueError as ve:
                            print(f"Ошибка при обработке значений для элемента {element_text}: {ve}")
                            continue
                    else:
                        not_found_elements.append(element_text)
                        print(f"Не найден код для элемента: {element_text}")
            
            if parsed_data:
                # Очищаем только главную таблицу
                for item in self.main_table.tree.get_children():
                    self.main_table.tree.delete(item)
                    
                # Загружаем новые данные в главную таблицу
                self.main_table.load_data(parsed_data)
                
                # Автоматически запускаем расчет
                self.update_calculations()
                
                if not_found_elements:
                    # Фильтруем список, убирая пустые строки и известные заголовки
                    filtered_not_found = [elem for elem in not_found_elements 
                                        if elem and elem not in ignore_headers]
                    if filtered_not_found:
                        messagebox.showwarning(
                            "Предупреждение",
                            f"Следующие элементы не были найдены в словаре:\n{', '.join(filtered_not_found)}"
                        )
                    else:
                        messagebox.showinfo("Успех", "Отчет успешно загружен и обработан")
                else:
                    messagebox.showinfo("Успех", "Отчет успешно загружен и обработан")
            else:
                raise Exception("Не удалось извлечь данные из файла")
                
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось загрузить отчет: {str(e)}")
            print(f"Подробности ошибки: {str(e)}")

    def calculate_time_compatibility(self, data: List[Dict], time: str) -> Dict[str, str]:
        # Получаем элементы с шкалой -3 и -2
        scale_elements = self.calculate_scale_results(data)
        elements_for_calculation = scale_elements.get(-3, '').split('; ') + scale_elements.get(-2, '').split('; ')
    
        # Распределяем элементы в зависимости от времени
        incompatible_elements = []
        compatible_elements = []
        for element in elements_for_calculation:
            for row in data:
                if row['element'] == element:
                    if row[time] == 'Да':
                        incompatible_elements.append(element)
                    else:
                        compatible_elements.append(element)
        
        # Вставляем первый элемент из "Несовместимость" в "Расчет совместимости"
        if incompatible_elements:
            first_incompatible = incompatible_elements[0]
            compatible_elements.append(first_incompatible)
            
            # Вставляем несовместимые элементы
            for row in data:
                if row['element'] == first_incompatible and row['incompatible']:
                    incompatible_elements.extend(row['incompatible'].split('; '))
        
        # Убираем дубликаты
        incompatible_elements = list(set(incompatible_elements))
        compatible_elements = list(set(compatible_elements))
        
        # Формируем итоговые строки
        compatible_str = '; '.join(compatible_elements)
        incompatible_str = '; '.join(incompatible_elements)
        calculation_elements_str = '; '.join(elements_for_calculation)
        
        # Формируем детали несовместимости
        incompatibility_details = []
        for element in compatible_elements:
            for row in data:
                if row['element'] == element and row['incompatible']:
                    incomp_elements = set(row['incompatible'].split('; ')) & set(incompatible_elements)
                    if incomp_elements:
                        incompatibility_details.append(f"{element}: {'; '.join(incomp_elements)}")
    
        return {
            'compatible': compatible_str,
            'incompatible': incompatible_str,
            'calculation_elements': calculation_elements_str,
            'incompatibility_details': '; '.join(incompatibility_details)
        }

    def update_calculations(self):
        data = self.main_table.get_all_data()
        
        # Update scale result table
        scale_data = self.calculate_scale_results(data)
        self.scale_table.update_data(scale_data)
        
        # Обработка утренней таблицы
        morning_data = self.process_morning_table(data)
        
        # Обновляем утреннюю таблицу
        self.morning_table.update_data(
            compatible=morning_data['compatible'],          
            incompatible=morning_data['morning_no'],       
            calculation_elements=morning_data['calculation_elements'],  
            incompatibility_details=morning_data['morning_yes']        
        )
        
        # Обновляем значения в ячейках [4,1] и [4,2] утренней таблицы
        items_morning = self.morning_table.tree.get_children()
        values_row2_morning = list(self.morning_table.tree.item(items_morning[1])['values'])
        values_row2_morning[1] = morning_data['incompatibilities']     
        values_row2_morning[2] = morning_data['incompatible_morning']  
        self.morning_table.tree.item(items_morning[1], values=values_row2_morning)
        
        # Получаем данные из утренней таблицы для дневной таблицы
        morning_incompatible = morning_data['incompatible_morning']  # [4,2]
        morning_no = morning_data['morning_no']  # [3,2]
        
        # Формируем данные для [5,1] дневной таблицы
        day_calculation_elements = []
        if self.day_table.data_order.get():  # Первый вариант: Несовместимость + время
            if morning_incompatible:
                day_calculation_elements.extend(morning_incompatible.split('; '))
            if morning_no:
                day_calculation_elements.extend(morning_no.split('; '))
        else:  # Второй вариант: Время + несовместимость
            if morning_no:
                day_calculation_elements.extend(morning_no.split('; '))
            if morning_incompatible:
                day_calculation_elements.extend(morning_incompatible.split('; '))
            
        day_calculation_elements = list(filter(None, day_calculation_elements))
        
        # Обработка дневной таблицы
        day_data = self.process_day_table(data, day_calculation_elements)
        
        # Обновляем дневную таблицу
        items_day = self.day_table.tree.get_children()
        
        # Обновляем основные значения
        values_row1_day = list(self.day_table.tree.item(items_day[0])['values'])
        values_row2_day = list(self.day_table.tree.item(items_day[1])['values'])
        values_row3_day = list(self.day_table.tree.item(items_day[2])['values'])
        
        values_row1_day[1] = day_data['compatible']  # [3,1]
        values_row1_day[2] = day_data['day_no']      # [3,2]
        values_row2_day[1] = day_data['incompatibilities']  # [4,1]
        values_row2_day[2] = day_data['incompatible_day']   # [4,2]
        values_row3_day[1] = day_data['calculation_elements']  # [5,1]
        values_row3_day[2] = day_data['day_yes']               # [5,2]
        
        # Обновляем все строки дневной таблицы
        self.day_table.tree.item(items_day[0], values=values_row1_day)
        self.day_table.tree.item(items_day[1], values=values_row2_day)
        self.day_table.tree.item(items_day[2], values=values_row3_day)
        
        # Заполняем ячейку [5,1] вечерней таблицы данными из дневной таблицы
        evening_calculation_elements = []
        if self.evening_table.data_order.get():  # Первый вариант: Несовместимость + время
            # Сначала добавляем данные из ячейки [4,2] дневной таблицы
            if day_data['incompatible_day']:
                evening_calculation_elements.extend(day_data['incompatible_day'].split('; '))
            # Затем добавляем данные из ячейки [3,2] дневной таблицы
            if day_data['day_no']:
                evening_calculation_elements.extend(day_data['day_no'].split('; '))
        else:  # Второй вариант: Время + несовместимость
            # Сначала добавляем данные из ячейки [3,2] дневной таблицы
            if day_data['day_no']:
                evening_calculation_elements.extend(day_data['day_no'].split('; '))
            # Затем добавляем данные из ячейки [4,2] дневной таблицы
            if day_data['incompatible_day']:
                evening_calculation_elements.extend(day_data['incompatible_day'].split('; '))
        
        evening_calculation_elements = list(filter(None, evening_calculation_elements))
        
        # Обрабатываем данные для вечерней таблицы
        evening_data = self.process_evening_table(data, evening_calculation_elements)
        
        # Обновляем вечернюю таблицу
        items_evening = self.evening_table.tree.get_children()
        
        values_row1_evening = list(self.evening_table.tree.item(items_evening[0])['values'])
        values_row2_evening = list(self.evening_table.tree.item(items_evening[1])['values'])
        values_row3_evening = list(self.evening_table.tree.item(items_evening[2])['values'])
        
        values_row1_evening[1] = evening_data['compatible']  # [3,1]
        values_row1_evening[2] = evening_data['evening_no']  # [3,2]
        values_row2_evening[1] = evening_data['incompatibilities']  # [4,1]
        values_row2_evening[2] = evening_data['incompatible_evening']  # [4,2]
        values_row3_evening[1] = evening_data['calculation_elements']  # [5,1]
        values_row3_evening[2] = evening_data['evening_yes']  # [5,2]
        
        # Обновляем все строки вечерней таблицы
        self.evening_table.tree.item(items_evening[0], values=values_row1_evening)
        self.evening_table.tree.item(items_evening[1], values=values_row2_evening)
        self.evening_table.tree.item(items_evening[2], values=values_row3_evening)

        # Заполняем строки 5 и 6 сводной таблицы
        items_summary = self.summary_table.tree.get_children()
        values_row5 = list(self.summary_table.tree.item(items_summary[0])['values'])  # Первая строка (индекс 0) - это строка 5
        values_row6 = list(self.summary_table.tree.item(items_summary[1])['values'])  # Вторая строка (индекс 1) - это строка 6
        values_row7 = list(self.summary_table.tree.item(items_summary[2])['values'])  # Третья строка (индекс 2) - это строка 7
        
        # Получаем данные из ячейки [3,1] каждой таблицы
        morning_compatible = morning_data['compatible']  # [3,1] утренней таблицы
        day_compatible = day_data['compatible']         # [3,1] дневной таблицы
        evening_compatible = evening_data['compatible'] # [3,1] вечерней таблицы
        
        # Заполняем ячейки [6,1], [6,2] и [6,3] сводной таблицы
        values_row6[1] = morning_compatible  # [6,1]
        values_row6[2] = day_compatible      # [6,2]
        values_row6[3] = evening_compatible  # [6,3]
        
        # Получаем несовместимые элементы для элементов из ячейки [6,1]
        incompatible_morning = set()
        if morning_compatible:
            for element in morning_compatible.split('; '):
                for row in data:
                    if row['element'] == element and row['incompatible']:
                        incompatible_morning.update(row['incompatible'].split('; '))
        
        # Получаем несовместимые элементы для элементов из ячейки [6,2]
        incompatible_day = set()
        if day_compatible:
            for element in day_compatible.split('; '):
                for row in data:
                    if row['element'] == element and row['incompatible']:
                        incompatible_day.update(row['incompatible'].split('; '))
        
        # Получаем несовместимые элементы для элементов из ячейки [6,3]
        incompatible_evening = set()
        if evening_compatible:
            for element in evening_compatible.split('; '):
                for row in data:
                    if row['element'] == element and row['incompatible']:
                        incompatible_evening.update(row['incompatible'].split('; '))
        
        # Заполняем ячейки [5,1], [5,2] и [5,3] сводной таблицы
        values_row5[1] = '; '.join(sorted(incompatible_morning))   # [5,1]
        values_row5[2] = '; '.join(sorted(incompatible_day))       # [5,2]
        values_row5[3] = '; '.join(sorted(incompatible_evening))   # [5,3]
        
        # Заполняем ячейку [7,1] данными из вечерней таблицы
        evening_no = evening_data['evening_no']  # [3,2] вечерней таблицы
        evening_incompatible = evening_data['incompatible_evening']  # [4,2] вечерней таблицы
        
        # Объединяем данные из [3,2] и [4,2] вечерней таблицы
        evening_combined = []
        if evening_no:
            evening_combined.extend(evening_no.split('; '))
        if evening_incompatible:
            evening_combined.extend(evening_incompatible.split('; '))
        
        # Записываем в [7,1]
        values_row7[1] = '; '.join(sorted(set(evening_combined)))
        
        # Проверяем каждый элемент из [7,1] на повтор с [5,1]
        elements_71 = values_row7[1].split('; ') if values_row7[1] else []
        elements_51 = values_row5[1].split('; ') if values_row5[1] else []
        elements_61 = values_row6[1].split('; ') if values_row6[1] else []
        
        # Создаем список элементов, которые останутся в [7,1]
        remaining_71_elements = []
        
        for element in elements_71:
            if element and element not in elements_51:  # Если элемента нет в [5,1]
                # Добавляем элемент к списку в [6,1]
                elements_61.append(element)
                
                # Получаем несовместимости для этого элемента
                for row in data:
                    if row['element'] == element and row['incompatible']:
                        # Добавляем несовместимости к списку в [5,1]
                        elements_51.extend(row['incompatible'].split('; '))
            else:
                # Если элемент есть в [5,1], оставляем его в [7,1]
                remaining_71_elements.append(element)
        
        # Обновляем значения в ячейках
        values_row5[1] = '; '.join(sorted(set(elements_51)))
        values_row6[1] = '; '.join(sorted(set(elements_61)))
        values_row7[1] = '; '.join(sorted(set(remaining_71_elements)))
        
        # Проверяем каждый элемент из [7,1] на повтор с [5,2]
        elements_71 = values_row7[1].split('; ') if values_row7[1] else []
        elements_52 = values_row5[2].split('; ') if values_row5[2] else []
        elements_62 = values_row6[2].split('; ') if values_row6[2] else []
        
        # Создаем список элементов, которые останутся в [7,1]
        remaining_71_elements = []
        
        for element in elements_71:
            if element and element not in elements_52:  # Если элемента нет в [5,2]
                # Добавляем элемент к списку в [6,2]
                elements_62.append(element)
                
                # Получаем несовместимости для этого элемента
                for row in data:
                    if row['element'] == element and row['incompatible']:
                        # Добавляем несовместимости к списку в [5,2]
                        elements_52.extend(row['incompatible'].split('; '))
            else:
                # Если элемент есть в [5,2], оставляем его в [7,1]
                remaining_71_elements.append(element)
        
        # Обновляем значения в ячейках
        values_row5[2] = '; '.join(sorted(set(elements_52)))
        values_row6[2] = '; '.join(sorted(set(elements_62)))
        values_row7[1] = '; '.join(sorted(set(remaining_71_elements)))
        
        # Проверяем каждый элемент из [7,1] на повтор с [5,3]
        elements_71 = values_row7[1].split('; ') if values_row7[1] else []
        elements_53 = values_row5[3].split('; ') if values_row5[3] else []
        elements_63 = values_row6[3].split('; ') if values_row6[3] else []
        
        # Создаем список элементов, которые останутся в [7,1]
        remaining_71_elements = []
        
        for element in elements_71:
            if element and element not in elements_53:  # Если элемента нет в [5,3]
                # Добавляем элемент к списку в [6,3]
                elements_63.append(element)
                
                # Получаем несовместимости для этого элемента
                for row in data:
                    if row['element'] == element and row['incompatible']:
                        # Добавляем несовместимости к списку в [5,3]
                        elements_53.extend(row['incompatible'].split('; '))
            else:
                # Если элемент есть в [5,3], оставляем его в [7,1]
                remaining_71_elements.append(element)
        
        # Обновляем значения в ячейках
        values_row5[3] = '; '.join(sorted(set(elements_53)))
        values_row6[3] = '; '.join(sorted(set(elements_63)))
        values_row7[1] = '; '.join(sorted(set(remaining_71_elements)))
        
        # Обновляем строки в сводной таблице
        self.summary_table.tree.item(items_summary[0], values=values_row5)  # Обновляем строку 5
        self.summary_table.tree.item(items_summary[1], values=values_row6)  # Обновляем строку 6
        self.summary_table.tree.item(items_summary[2], values=values_row7)  # Обновляем строку 7

        # Подсчитываем количество элементов для каждого времени приема
        morning_count = len(morning_compatible.split('; ')) if morning_compatible else 0
        day_count = len(day_compatible.split('; ')) if day_compatible else 0
        evening_count = len(evening_compatible.split('; ')) if evening_compatible else 0

        # Обновляем отчет и визуализацию
        self.summary_table.update_report(morning_count, day_count, evening_count)
        
        # Обновляем таблицы распределения
        self.distribution_table1.update_values(data)
        self.distribution_table2.update_values(data)
        self.distribution_table3.update_values(data)
        self.distribution_table4.update_values(data)
        self.distribution_table5.update_values(data)

    def load_initial_data(self):
        self.main_table.load_data(INITIAL_DATA)
        self.update_calculations()

    def calculate_scale_results(self, data: List[Dict]) -> Dict[int, str]:
        scale_elements = {i: [] for i in range(-3, 4)}
        for row in data:
            try:
                scale = int(row['scale'])
                scale_elements[scale].append(row['element'])
            except ValueError:
                continue
        return {scale: '; '.join(elements) for scale, elements in scale_elements.items()}

    def get_without_time_elements(self, data: List[Dict]) -> str:
        elements = []
        for row in data:
            if row['morning'] == 'Да' and row['day'] == 'Да' and row['evening'] == 'Да':
                elements.append(row['element'])
        return '; '.join(elements)

    def get_all_incompatible(self, time_data: List[Dict]) -> str:
        all_incompatible = set()
        for data in time_data:
            if data['incompatible']:
                all_incompatible.update(data['incompatible'].split('; '))
        return '; '.join(sorted(all_incompatible))
    
    def process_morning_table(self, data: List[Dict]):
        # Получаем элементы со шкалой -3 и -2
        scale_elements = self.calculate_scale_results(data)
        calculation_elements = (scale_elements.get(-3, '').split('; ') + 
                              scale_elements.get(-2, '').split('; '))
        calculation_elements = list(filter(None, calculation_elements))
        
        # Разделяем на элементы с "Да" и "Нет"
        morning_yes = []
        morning_no = []
        for element in calculation_elements:
            for row in data:
                if row['element'] == element:
                    if row['morning'] == 'Да':
                        morning_yes.append(element)
                    else:
                        morning_no.append(element)
                    break
        
        compatible_elements = []  # Элементы для приема утром [3,1]
        incompatible_morning = []  # Несовместимые в приеме [4,2]
        all_incompatibilities = set()  # Множество для всех несовместимостей
        
        # Начинаем с первого элемента из morning_yes
        if morning_yes:
            current_element = morning_yes[0]
            compatible_elements.append(current_element)
            
            # Получаем несовместимости для первого элемента
            for row in data:
                if row['element'] == current_element and row['incompatible']:
                    all_incompatibilities.update(row['incompatible'].split('; '))
                    break
                
            # Проверяем каждый следующий элемент
            for next_element in morning_yes[1:]:
                is_compatible = True
                
                # Проверяем несовместимость со всеми уже добавленными элементами
                for added_element in compatible_elements:
                    for row in data:
                        if row['element'] == added_element and row['incompatible']:
                            if next_element in row['incompatible'].split('; '):
                                is_compatible = False
                                incompatible_morning.append(next_element)
                                break
                    if not is_compatible:
                        break
                
                # Если элемент совместим, добавляем его
                if is_compatible:
                    compatible_elements.append(next_element)
                    
                    # Добавляем его несовместимости
                    for row in data:
                        if row['element'] == next_element and row['incompatible']:
                            all_incompatibilities.update(row['incompatible'].split('; '))
        
        return {
            'compatible': '; '.join(compatible_elements),  # [3,1]
            'morning_no': '; '.join(morning_no),  # [3,2]
            'incompatibilities': '; '.join(sorted(all_incompatibilities)) if all_incompatibilities else '',  # [4,1]
            'incompatible_morning': '; '.join(incompatible_morning),  # [4,2]
            'calculation_elements': '; '.join(calculation_elements),  # [5,1]
            'morning_yes': '; '.join(morning_yes)  # [5,2]
        }

    def process_day_table(self, data: List[Dict], day_calculation_elements: List[str]):
        # Разделяем на элементы с "Да" и "Нет"
        day_yes = []  # Элементы с "Да" в колонке "День"
        day_no = []   # Элементы с "Нет" в колонке "День"
        
        for element in day_calculation_elements:
            for row in data:
                if row['element'] == element:
                    if row['day'] == 'Да':
                        day_yes.append(element)
                    else:
                        day_no.append(element)
                    break
        
        compatible_elements = []  # Элементы для [3,1]
        incompatible_day = []    # Элементы для [4,2]
        all_incompatibilities = set()  # Множество для всех несовместимостей
        
        # Начинаем с первого элемента из day_yes
        if day_yes:
            current_element = day_yes[0]
            compatible_elements.append(current_element)
            
            # Получаем несовместимости для первого элемента
            for row in data:
                if row['element'] == current_element and row['incompatible']:
                    all_incompatibilities.update(row['incompatible'].split('; '))
                    break
            
            # Проверяем каждый следующий элемент
            for next_element in day_yes[1:]:
                is_compatible = True
                
                # Проверяем несовместимость со всеми уже добавленными элементами
                for added_element in compatible_elements:
                    for row in data:
                        if row['element'] == added_element and row['incompatible']:
                            if next_element in row['incompatible'].split('; '):
                                is_compatible = False
                                incompatible_day.append(next_element)
                                break
                    if not is_compatible:
                        break
                
                # Если элемент совместим, добавляем его
                if is_compatible:
                    compatible_elements.append(next_element)
                    
                    # Добавляем его несовместимости
                    for row in data:
                        if row['element'] == next_element and row['incompatible']:
                            all_incompatibilities.update(row['incompatible'].split('; '))
            
        return {
            'compatible': '; '.join(compatible_elements),  # [3,1]
            'day_no': '; '.join(day_no),  # [3,2]
            'incompatibilities': '; '.join(sorted(all_incompatibilities)) if all_incompatibilities else '',  # [4,1]
            'incompatible_day': '; '.join(incompatible_day),  # [4,2]
            'calculation_elements': '; '.join(day_calculation_elements),  # [5,1]
            'day_yes': '; '.join(day_yes)  # [5,2]
        }

    def process_evening_table(self, data: List[Dict], evening_calculation_elements: List[str]):
        # Разделяем элементы на "Да" и "Нет" по колонке "Вечер"
        evening_yes = []  # Элементы с "Да" 
        evening_no = []   # Элементы с "Нет"
        
        for element in evening_calculation_elements:
            for row in data:
                if row['element'] == element:
                    if row['evening'] == 'Да':
                        evening_yes.append(element)
                    else:
                        evening_no.append(element)
                    break
        
        compatible_elements = []  # Для [3,1]
        incompatible_evening = []    # Для [4,2]
        all_incompatibilities = set()  # Множество для всех несовместимостей
        
        # Если есть элементы с "Да"
        if evening_yes:
            current_element = evening_yes[0]
            compatible_elements.append(current_element)
            
            # Получаем несовместимости для первого элемента
            for row in data:
                if row['element'] == current_element and row['incompatible']:
                    all_incompatibilities.update(row['incompatible'].split('; '))
                    break
            
            # Проверяем каждый следующий элемент
            for next_element in evening_yes[1:]:
                is_compatible = True
                
                # Проверяем несовместимость со всеми уже добавленными
                for added_element in compatible_elements:
                    for row in data:
                        if row['element'] == added_element and row['incompatible']:
                            if next_element in row['incompatible'].split('; '):
                                is_compatible = False
                                incompatible_evening.append(next_element)
                                break
                    if not is_compatible:
                        break
                
                # Если элемент совместим, добавляем его
                if is_compatible:
                    compatible_elements.append(next_element)
                    
                    # Добавляем его несовместимости
                    for row in data:
                        if row['element'] == next_element and row['incompatible']:
                            all_incompatibilities.update(row['incompatible'].split('; '))
        
        return {
            'compatible': '; '.join(compatible_elements),  # [3,1] - совместимые элементы
            'evening_no': '; '.join(evening_no),  # [3,2] - элементы с "Нет"
            'incompatibilities': '; '.join(sorted(all_incompatibilities)) if all_incompatibilities else '',  # [4,1] - все несовместимости
            'incompatible_evening': '; '.join(incompatible_evening),  # [4,2] - несовместимые элементы
            'calculation_elements': '; '.join(evening_calculation_elements),  # [5,1] - элементы для расчета
            'evening_yes': '; '.join(evening_yes)  # [5,2] - элементы с "Да"
        }

    def update_patient_info(self, info: dict):
        """Обновляет информацию о пациенте из шапки программы"""
        # Извлекаем информацию из текста меток
        header_text1 = self.patient_info_label1.cget("text")
        header_text2 = self.patient_info_label2.cget("text")
        
        # Парсим информацию из текста
        if "Имя:" in header_text1:
            self.patient_info['client_name'] = header_text1.split("Имя:")[1].split("Пол:")[0].strip()
        if "Пол:" in header_text1:
            self.patient_info['gender'] = header_text1.split("Пол:")[1].split("Возраст:")[0].strip()
        if "Возраст:" in header_text1:
            self.patient_info['client_age'] = header_text1.split("Возраст:")[1].split("Время тестирования:")[0].strip()
        if "Время тестирования:" in header_text1:
            self.patient_info['test_date'] = header_text1.split("Время тестирования:")[1].strip()
        if "Телосложение:" in header_text2:
            self.patient_info['body_type'] = header_text2.split("Телосложение:")[1].strip()
        
        print(f"Обновлена информация о пациенте: {self.patient_info}")  # Для отладки

    def save_report(self):
        """Сохраняет отчет с информацией о пациенте"""
        try:
            # Обновляем информацию о пациенте перед сохранением
            self.update_patient_info({})
            
            # Создаем диалог сохранения файла
            file_path = filedialog.asksaveasfilename(
                defaultextension=".pptx",
                filetypes=[("PowerPoint files", "*.pptx"), ("PDF files", "*.pdf")],
                title="Сохранить отчет как..."
            )
            
            if not file_path:
                return

            # Сохраняем в Google Sheets через экземпляр SummaryCompatibilityTable
            try:
                # Обновляем информацию о пациенте в summary_table
                self.summary_table.update_patient_info(self.patient_info)
                # Вызываем метод сохранения в Google Sheets
                self.summary_table.save_to_google_sheets()
            except Exception as e:
                print(f"Ошибка при сохранении в Google Sheets: {str(e)}")
                messagebox.showwarning("Предупреждение", 
                    "Не удалось сохранить данные в Google таблицу. Отчет будет сохранен локально.")

            # Сохраняем в лог-файл
            try:
                self.save_to_log(file_path)
            except Exception as e:
                print(f"Ошибка при сохранении в лог: {str(e)}")
                
            # Сохраняем отчет
            if file_path.endswith('.pdf'):
                self.save_to_pdf(file_path)
            else:
                self.save_to_pptx(file_path)

        except Exception as e:
            print(f"Ошибка при сохранении отчета: {str(e)}")
            messagebox.showerror("Ошибка", f"Не удалось сохранить отчет: {str(e)}")

    def save_to_log(self, report_path):
        """Сохраняет информацию о сохраненном отчете в лог-файл"""
        try:
            log_file = "reports_log.txt"
            current_time = datetime.now().strftime("%d.%m.%Y %H:%M")
            
            # Формируем строку для лога
            log_entry = (
                f"Дата записи: {current_time}\n"
                f"Имя клиента: {self.patient_info.get('client_name', '')}\n"
                f"Пол: {self.patient_info.get('gender', '')}\n"
                f"Возраст: {self.patient_info.get('client_age', '')}\n"
                f"Дата проверки: {self.patient_info.get('test_date', '')}\n"
                f"Путь к отчету: {report_path}\n"
                f"{'='*50}\n"
            )
            
            # Открываем файл в режиме добавления
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
                
            print(f"Информация успешно добавлена в лог-файл: {log_file}")
            
        except Exception as e:
            print(f"Ошибка при сохранении в лог-файл: {str(e)}")
            raise

def main():
    app = CompatibilityCalculator()
    app.mainloop()

if __name__ == "__main__":
    main()
