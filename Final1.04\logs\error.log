[2025-03-03 23:33:14] ERROR в save_report: Ош<PERSON>бка при сохранении в Google Sheets: <Response [200]>
[2025-03-03 23:33:14] ERROR в save_report: Детали ошибки: Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "gspread", "client.py"), line 134, in open
    properties = finditem(
                 ^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "gspread", "utils.py"), line 218, in finditem
    return next(item for item in seq if func(item))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
StopIteration

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File os.path.join("D:", "Projects", "MedicalMind", "PreFinal", "save_report.py"), line 549, in save_to_google_sheets
    logging.info("Доступ к сервисному аккаунту получен успешно")
         ^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "gspread", "client.py"), line 139, in open
    raise SpreadsheetNotFound(response) from ex
gspread.exceptions.SpreadsheetNotFound: <Response [200]>

[2025-03-03 23:33:15] ERROR в app: Файл не найден: D:\Projects\MedicalMind\PreFinal\reports\Васильева_Анна_03_12_202417-16.pdf
[2025-03-03 23:33:48] ERROR в save_report: Ошибка при сохранении в Google Sheets: <Response [200]>
[2025-03-03 23:33:48] ERROR в save_report: Детали ошибки: Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "gspread", "client.py"), line 134, in open
    properties = finditem(
                 ^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "gspread", "utils.py"), line 218, in finditem
    return next(item for item in seq if func(item))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
StopIteration

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File os.path.join("D:", "Projects", "MedicalMind", "PreFinal", "save_report.py"), line 549, in save_to_google_sheets
    logging.info("Доступ к сервисному аккаунту получен успешно")
         ^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "gspread", "client.py"), line 139, in open
    raise SpreadsheetNotFound(response) from ex
gspread.exceptions.SpreadsheetNotFound: <Response [200]>

[2025-03-03 23:33:49] ERROR в app: Файл не найден: D:\Projects\MedicalMind\PreFinal\reports\Мосеева_Ольга_11_01_202520-12.pdf
[2025-03-03 23:34:18] ERROR в save_report: Ошибка при сохранении в Google Sheets: <Response [200]>
[2025-03-03 23:34:18] ERROR в save_report: Детали ошибки: Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "gspread", "client.py"), line 134, in open
    properties = finditem(
                 ^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "gspread", "utils.py"), line 218, in finditem
    return next(item for item in seq if func(item))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
StopIteration

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File os.path.join("D:", "Projects", "MedicalMind", "PreFinal", "save_report.py"), line 549, in save_to_google_sheets
    # Определяем новые заголовки
        ^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "gspread", "client.py"), line 139, in open
    raise SpreadsheetNotFound(response) from ex
gspread.exceptions.SpreadsheetNotFound: <Response [200]>

[2025-03-03 23:34:19] ERROR в app: Файл не найден: D:\Projects\MedicalMind\PreFinal\reports\Александр_Александрович_22_01_202512-40.pdf
[2025-03-04 01:15:41] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1965, in _exec_single_context
    self.dialect.do_execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "default.py"), line 921, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: no such column: users.created_at

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 284, in decorated_view
    elif not current_user.is_authenticated:
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "werkzeug", "local.py"), line 311, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "werkzeug", "local.py"), line 515, in _get_current_object
    return get_name(local())
                    ^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 25, in <lambda>
    current_user = LocalProxy(lambda: _get_user())
                                      ^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 372, in _get_user
    current_app.login_manager._load_user()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "login_manager.py"), line 364, in _load_user
    user = self._user_callback(user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "PreFinal", "app.py"), line 45, in load_user
    return User.query.get(int(id))
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "<string>", line 2, in get
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "util", "deprecations.py"), line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 1131, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 1140, in _get_impl
    return self.session._get_impl(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 3700, in _get_impl
    return db_load_fn(
           ^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "loading.py"), line 666, in load_on_pk_identity
    session.execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 2262, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 2144, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "context.py"), line 293, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1412, in execute
    return meth(
           ^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "sql", "elements.py"), line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1635, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1844, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1984, in _exec_single_context
    self._handle_dbapi_exception(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 2339, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1965, in _exec_single_context
    self.dialect.do_execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "default.py"), line 921, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.password_hash AS users_password_hash, users.is_active AS users_is_active, users.is_admin AS users_is_admin, users.created_at AS users_created_at, users.last_login AS users_last_login 
FROM users 
WHERE users.id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-04 01:15:41] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 01:24:37] ERROR в save_report: Ошибка при получении данных из таблицы: The read operation timed out
[2025-03-04 01:24:37] ERROR в save_report: Ошибка при сохранении в Google Sheets: The read operation timed out
[2025-03-04 01:24:37] ERROR в save_report: Ошибка при сохранении в Google Sheets: The read operation timed out
[2025-03-04 01:26:15] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "PreFinal", "app.py"), line 610, in index
    return render_template('index.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("D:", "Projects", "MedicalMind", "PreFinal", "templates", "index.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("D:", "Projects", "MedicalMind", "PreFinal", "templates", "base.html"), line 31, in top-level template code
    <a class="nav-link" href="{{ url_for('admin') }}">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1697, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1686, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "werkzeug", "routing", "map.py"), line 950, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'admin'. Did you mean 'admin_panel' instead?
[2025-03-04 01:26:15] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 01:26:20] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "PreFinal", "app.py"), line 610, in index
    return render_template('index.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("D:", "Projects", "MedicalMind", "PreFinal", "templates", "index.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("D:", "Projects", "MedicalMind", "PreFinal", "templates", "base.html"), line 31, in top-level template code
    <a class="nav-link" href="{{ url_for('admin') }}">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1697, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1686, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "werkzeug", "routing", "map.py"), line 950, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'admin'. Did you mean 'admin_panel' instead?
[2025-03-04 01:26:20] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 01:27:09] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "PreFinal", "app.py"), line 610, in index
    return render_template('index.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("D:", "Projects", "MedicalMind", "PreFinal", "templates", "index.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("D:", "Projects", "MedicalMind", "PreFinal", "templates", "base.html"), line 31, in top-level template code
    <a class="nav-link" href="{{ url_for('admin') }}">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1697, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1686, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "werkzeug", "routing", "map.py"), line 950, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'admin'. Did you mean 'admin_panel' instead?
[2025-03-04 01:27:09] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 02:55:35] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1965, in _exec_single_context
    self.dialect.do_execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "default.py"), line 921, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: no such column: users.name

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 284, in decorated_view
    elif not current_user.is_authenticated:
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "werkzeug", "local.py"), line 311, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "werkzeug", "local.py"), line 515, in _get_current_object
    return get_name(local())
                    ^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 25, in <lambda>
    current_user = LocalProxy(lambda: _get_user())
                                      ^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 372, in _get_user
    current_app.login_manager._load_user()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "login_manager.py"), line 364, in _load_user
    user = self._user_callback(user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 45, in load_user
    return User.query.get(int(id))
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "<string>", line 2, in get
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "util", "deprecations.py"), line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 1131, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 1140, in _get_impl
    return self.session._get_impl(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 3700, in _get_impl
    return db_load_fn(
           ^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "loading.py"), line 666, in load_on_pk_identity
    session.execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 2262, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 2144, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "context.py"), line 293, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1412, in execute
    return meth(
           ^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "sql", "elements.py"), line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1635, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1844, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1984, in _exec_single_context
    self._handle_dbapi_exception(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 2339, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1965, in _exec_single_context
    self.dialect.do_execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "default.py"), line 921, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.name
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.name AS users_name, users.email AS users_email, users.password_hash AS users_password_hash, users.is_active AS users_is_active, users.is_admin AS users_is_admin, users.created_at AS users_created_at, users.last_login AS users_last_login 
FROM users 
WHERE users.id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-04 02:55:35] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 02:59:06] ERROR в app: Exception on /admin/user/1 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1526, in admin_user_details
    return render_template('admin_user_details.html', title=f'Пользователь {user.username}',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 02:59:06] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:00:51] ERROR в app: Exception on /admin/user/1 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1526, in admin_user_details
    return render_template('admin_user_details.html', title=f'Пользователь {user.username}',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:00:51] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:03:21] ERROR в app: Exception on /admin/user/2 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1533, in admin_user_details
    return render_template('admin_user_details.html', title=f'Пользователь {user.username}',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:03:21] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:03:31] ERROR в app: Exception on /admin/user/2 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1533, in admin_user_details
    return render_template('admin_user_details.html', title=f'Пользователь {user.username}',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:03:31] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:04:22] ERROR в app: Exception on /admin/user/2 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1533, in admin_user_details
    return render_template('admin_user_details.html', title=f'Пользователь {user.username}',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:04:22] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:04:44] ERROR в app: Exception on /admin/user/1 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1533, in admin_user_details
    return render_template('admin_user_details.html', title=f'Пользователь {user.username}',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:04:44] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:05:23] ERROR в app: Exception on /admin/user/1 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1533, in admin_user_details
    return render_template('admin_user_details.html', title=f'Пользователь {user.username}',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:05:23] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:05:42] ERROR в app: Exception on /admin/user/1 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1533, in admin_user_details
    return render_template('admin_user_details.html', title=f'Пользователь {user.username}',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:05:42] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:05:54] ERROR в app: Exception on /admin/user/1 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1533, in admin_user_details
    return render_template('admin_user_details.html', title=f'Пользователь {user.username}',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:05:54] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:06:05] ERROR в app: Exception on /admin/user/1 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1533, in admin_user_details
    return render_template('admin_user_details.html', title=f'Пользователь {user.username}',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:06:05] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:06:43] ERROR в app: Exception on /admin/user/1 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1533, in admin_user_details
    return render_template('admin_user_details.html', title=f'Пользователь {user.username}',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:06:43] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:07:14] ERROR в app: Exception on /admin/user/1 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1533, in admin_user_details
    return render_template('admin_user_details.html', title=f'Пользователь {user.username}',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:07:14] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:09:28] ERROR в app: Exception on /admin/user/1 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1533, in admin_user_details
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:09:28] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:10:11] ERROR в app: Exception on /admin/user/1 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1533, in admin_user_details
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:10:11] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:11:20] ERROR в app: Exception on /admin/user/1 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1533, in admin_user_details
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:11:20] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:13:14] ERROR в app: Exception on /admin/user/1 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1533, in admin_user_details
    )
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:13:14] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:13:18] ERROR в app: Exception on /admin/user/1 [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "app.py"), line 1533, in admin_user_details
    )
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "templating.py"), line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 1304, in render
    self.environment.handle_exception()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "jinja2", "environment.py"), line 939, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 1, in top-level template code
    {% extends "base.html" %}
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "base.html"), line 84, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final", "templates", "admin_user_details.html"), line 145, in block 'content'
    <p class="mb-0">{{ activities|length if activities else 0 }} действий</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object of type 'Query' has no len()
[2025-03-04 03:13:18] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-04 03:13:39] ERROR в app: Ошибка при отображении профиля пользователя: object of type 'Query' has no len()
[2025-03-04 03:13:49] ERROR в app: Ошибка при отображении профиля пользователя: object of type 'Query' has no len()
[2025-03-04 03:13:52] ERROR в app: Ошибка при отображении профиля пользователя: object of type 'Query' has no len()
[2025-03-04 03:16:20] ERROR в app: Ошибка при отображении профиля пользователя: object of type 'Query' has no len()
[2025-03-04 03:26:46] ERROR в app: Ошибка при сохранении отчета: type object 'ReportProcessor' has no attribute 'MockTree'
[2025-03-04 03:26:49] ERROR в app: Ошибка при сохранении отчета: type object 'ReportProcessor' has no attribute 'MockTree'
[2025-03-04 03:29:07] ERROR в app: Ошибка при сохранении отчета: type object 'ReportProcessor' has no attribute 'MockTree'
[2025-03-04 03:29:24] ERROR в app: Ошибка при сохранении отчета: type object 'ReportProcessor' has no attribute 'MockTree'
[2025-03-04 03:29:25] ERROR в app: Ошибка при сохранении отчета: type object 'ReportProcessor' has no attribute 'MockTree'
[2025-03-04 03:32:35] ERROR в app: Ошибка при сохранении отчета: type object 'ReportProcessor' has no attribute 'MockTree'
[2025-03-04 03:32:55] ERROR в save_report: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:32:55] ERROR в save_report: Ошибка при автоматическом сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:32:55] ERROR в save_report: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:32:55] ERROR в app: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:34:00] ERROR в save_report: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:34:00] ERROR в save_report: Ошибка при автоматическом сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:34:00] ERROR в save_report: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:34:00] ERROR в app: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:35:51] ERROR в save_report: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:35:51] ERROR в save_report: Ошибка при автоматическом сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:35:51] ERROR в save_report: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:35:51] ERROR в app: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:36:13] ERROR в save_report: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:36:13] ERROR в save_report: Ошибка при автоматическом сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:36:13] ERROR в save_report: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:36:13] ERROR в app: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:36:30] ERROR в save_report: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:36:30] ERROR в save_report: Ошибка при автоматическом сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:36:30] ERROR в save_report: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:36:30] ERROR в app: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:37:50] ERROR в save_report: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:37:50] ERROR в save_report: Ошибка при автоматическом сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:37:50] ERROR в save_report: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:37:50] ERROR в app: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:38:45] ERROR в save_report: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:38:45] ERROR в save_report: Ошибка при автоматическом сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:38:45] ERROR в save_report: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-04 03:38:45] ERROR в app: Ошибка при сохранении отчета: 'MockTree' object has no attribute 'get_children'
[2025-03-05 04:24:25] ERROR в save_report: Ошибка при сохранении отчета: cannot access local variable 'deficits_text' where it is not associated with a value
[2025-03-05 04:24:30] ERROR в save_report: Ошибка при сохранении отчета: cannot access local variable 'deficits_text' where it is not associated with a value
[2025-03-05 04:29:46] ERROR в save_report: Ошибка при сохранении отчета: 0
[2025-03-05 04:29:46] ERROR в app: Ошибка при создании отчета: 0
[2025-03-05 04:29:48] ERROR в save_report: Ошибка при сохранении отчета: 0
[2025-03-05 04:29:48] ERROR в app: Ошибка при создании отчета: 0
[2025-03-05 04:38:10] ERROR в save_report: Ошибка при сохранении отчета: 0
[2025-03-05 04:38:10] ERROR в app: Ошибка при создании отчета: 0
[2025-03-05 04:41:59] ERROR в save_report: Ошибка при сохранении отчета: 0
[2025-03-05 04:41:59] ERROR в app: Ошибка при создании отчета: 0
[2025-03-06 04:20:55] ERROR в save_report: Ошибка при сохранении отчета: '_Cell' object has no attribute 'borders'
[2025-03-06 04:20:55] ERROR в save_report: Ошибка при автоматическом сохранении отчета: '_Cell' object has no attribute 'borders'
[2025-03-06 04:20:55] ERROR в save_report: Ошибка при сохранении отчета: '_Cell' object has no attribute 'borders'
[2025-03-06 04:21:32] ERROR в save_report: Ошибка при сохранении отчета: '_Cell' object has no attribute 'borders'
[2025-03-06 04:21:32] ERROR в save_report: Ошибка при автоматическом сохранении отчета: '_Cell' object has no attribute 'borders'
[2025-03-06 04:21:32] ERROR в save_report: Ошибка при сохранении отчета: '_Cell' object has no attribute 'borders'
[2025-03-06 04:22:28] ERROR в save_report: Ошибка при сохранении отчета: '_Cell' object has no attribute 'borders'
[2025-03-06 04:22:28] ERROR в save_report: Ошибка при автоматическом сохранении отчета: '_Cell' object has no attribute 'borders'
[2025-03-06 04:22:28] ERROR в save_report: Ошибка при сохранении отчета: '_Cell' object has no attribute 'borders'
[2025-03-06 04:51:37] ERROR в app: Exception on /admin/get-latest-reports [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1965, in _exec_single_context
    self.dialect.do_execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "default.py"), line 921, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: no such column: report_logs.client_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 2047, in get_latest_reports
    latest_reports = ReportLog.query.order_by(ReportLog.created_at.desc()).limit(5).all()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 2688, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 2842, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 2262, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 2144, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "context.py"), line 293, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1412, in execute
    return meth(
           ^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "sql", "elements.py"), line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1635, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1844, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1984, in _exec_single_context
    self._handle_dbapi_exception(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 2339, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1965, in _exec_single_context
    self.dialect.do_execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "default.py"), line 921, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 04:51:37] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 04:51:57] ERROR в app: Exception on /admin [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1965, in _exec_single_context
    self.dialect.do_execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "default.py"), line 921, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: no such column: report_logs.client_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 1477, in admin_panel
    total_reports = ReportLog.query.count()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 3127, in count
    self._legacy_from_self(col).enable_eagerloads(False).scalar()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 2820, in scalar
    ret = self.one()
          ^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 2793, in one
    return self._iter().one()  # type: ignore
           ^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 2842, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 2262, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 2144, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "context.py"), line 293, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1412, in execute
    return meth(
           ^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "sql", "elements.py"), line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1635, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1844, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1984, in _exec_single_context
    self._handle_dbapi_exception(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 2339, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1965, in _exec_single_context
    self.dialect.do_execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "default.py"), line 921, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT count(*) AS count_1 
FROM (SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 04:51:57] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 04:53:02] ERROR в app: Exception on /admin [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1965, in _exec_single_context
    self.dialect.do_execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "default.py"), line 921, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: no such column: report_logs.client_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 1477, in admin_panel
    total_reports = ReportLog.query.count()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 3127, in count
    self._legacy_from_self(col).enable_eagerloads(False).scalar()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 2820, in scalar
    ret = self.one()
          ^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 2793, in one
    return self._iter().one()  # type: ignore
           ^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 2842, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 2262, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 2144, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "context.py"), line 293, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1412, in execute
    return meth(
           ^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "sql", "elements.py"), line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1635, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1844, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1984, in _exec_single_context
    self._handle_dbapi_exception(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 2339, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1965, in _exec_single_context
    self.dialect.do_execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "default.py"), line 921, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT count(*) AS count_1 
FROM (SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 04:53:02] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 04:55:35] ERROR в app: Exception on /admin [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1965, in _exec_single_context
    self.dialect.do_execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "default.py"), line 921, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: no such column: report_logs.client_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 1477, in admin_panel
    total_reports = ReportLog.query.count()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 3127, in count
    self._legacy_from_self(col).enable_eagerloads(False).scalar()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 2820, in scalar
    ret = self.one()
          ^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 2793, in one
    return self._iter().one()  # type: ignore
           ^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 2842, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 2262, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 2144, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "context.py"), line 293, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1412, in execute
    return meth(
           ^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "sql", "elements.py"), line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1635, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1844, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1984, in _exec_single_context
    self._handle_dbapi_exception(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 2339, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1965, in _exec_single_context
    self.dialect.do_execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "default.py"), line 921, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT count(*) AS count_1 
FROM (SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 04:55:35] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 04:55:47] ERROR в app: Exception on /admin [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1965, in _exec_single_context
    self.dialect.do_execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "default.py"), line 921, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: no such column: report_logs.client_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("d:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 1477, in admin_panel
    total_reports = ReportLog.query.count()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 3127, in count
    self._legacy_from_self(col).enable_eagerloads(False).scalar()
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 2820, in scalar
    ret = self.one()
          ^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 2793, in one
    return self._iter().one()  # type: ignore
           ^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "query.py"), line 2842, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 2262, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "session.py"), line 2144, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "orm", "context.py"), line 293, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1412, in execute
    return meth(
           ^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "sql", "elements.py"), line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1635, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1844, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1984, in _exec_single_context
    self._handle_dbapi_exception(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 2339, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "base.py"), line 1965, in _exec_single_context
    self.dialect.do_execute(
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "sqlalchemy", "engine", "default.py"), line 921, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT count(*) AS count_1 
FROM (SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 04:55:47] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 05:01:16] ERROR в app: Ошибка при получении количества отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT count(*) AS count_1 
FROM (SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:01:16] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:02:31] ERROR в app: Ошибка при получении количества отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT count(*) AS count_1 
FROM (SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:02:31] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:02:39] ERROR в app: Ошибка при отображении клиентов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs 
WHERE ? = report_logs.client_id]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:02:39] ERROR в app: Ошибка при получении количества отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT count(*) AS count_1 
FROM (SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:02:39] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:03:10] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:03:40] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:03:42] ERROR в app: Ошибка при удалении пользователя: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs 
WHERE ? = report_logs.user_id]
[parameters: (2,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:03:42] ERROR в app: Ошибка при получении количества отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT count(*) AS count_1 
FROM (SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:03:42] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:03:54] ERROR в app: Ошибка при удалении пользователя: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs 
WHERE ? = report_logs.user_id]
[parameters: (2,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:03:54] ERROR в app: Ошибка при получении количества отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT count(*) AS count_1 
FROM (SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:03:54] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:04:25] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:04:55] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:05:25] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:05:45] ERROR в app: Ошибка при отображении отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:05:45] ERROR в app: Ошибка при получении количества отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT count(*) AS count_1 
FROM (SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:05:45] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:06:16] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:06:46] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:07:16] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:07:46] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:08:17] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:08:47] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:09:57] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:10:30] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:10:32] ERROR в app: Ошибка при получении количества отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT count(*) AS count_1 
FROM (SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:10:32] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:10:40] ERROR в app: Ошибка при удалении пользователя: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs 
WHERE ? = report_logs.user_id]
[parameters: (2,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:10:40] ERROR в app: Ошибка при получении количества отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT count(*) AS count_1 
FROM (SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:10:40] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:10:45] ERROR в app: Ошибка при отображении клиентов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs 
WHERE ? = report_logs.client_id]
[parameters: (4,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:10:45] ERROR в app: Ошибка при получении количества отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT count(*) AS count_1 
FROM (SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:10:45] ERROR в app: Ошибка при получении последних отчетов: (sqlite3.OperationalError) no such column: report_logs.client_id
[SQL: SELECT report_logs.id AS report_logs_id, report_logs.user_id AS report_logs_user_id, report_logs.patient_name AS report_logs_patient_name, report_logs.client_id AS report_logs_client_id, report_logs.filename AS report_logs_filename, report_logs.created_at AS report_logs_created_at, report_logs.report_type AS report_logs_report_type, report_logs.is_successful AS report_logs_is_successful, report_logs.error_message AS report_logs_error_message 
FROM report_logs ORDER BY report_logs.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (5, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-03-06 05:28:34] ERROR в app: Файл не найден: D:\Projects\MedicalMind\Final1.0\reports\Наташа_сестра_20250303204302.pptx
[2025-03-06 06:07:46] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 649, in index
    (UserActivity.activity_type == 'upload') &
     ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'UserActivity' has no attribute 'activity_type'
[2025-03-06 06:07:46] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:07:48] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 649, in index
    (UserActivity.activity_type == 'upload') &
     ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'UserActivity' has no attribute 'activity_type'
[2025-03-06 06:07:48] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:08:15] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 649, in index
    (UserActivity.activity_type == 'upload') &
     ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'UserActivity' has no attribute 'activity_type'
[2025-03-06 06:08:15] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:08:56] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 649, in index
    (ReportLog.created_at - UserActivity.timestamp).label('duration')
     ~~~~~~~~~~~~~~~~~~~~~^~~~~
AttributeError: type object 'UserActivity' has no attribute 'activity_type'
[2025-03-06 06:08:56] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:10:47] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 671, in index
    func.max(UserActivity.created_at).label('upload_time')
             ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'UserActivity' has no attribute 'created_at'
[2025-03-06 06:10:47] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:11:46] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 671, in index
    func.max(UserActivity.timestamp).label('upload_time')
             ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'UserActivity' has no attribute 'created_at'
[2025-03-06 06:11:46] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:11:57] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 673, in index
    UserActivity.type == 'upload',
    ^^^^^^^^^^^^^^^^^
AttributeError: type object 'UserActivity' has no attribute 'type'
[2025-03-06 06:11:57] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:13:11] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 673, in index
    reports_percent = 0
    ^^^^^^^^^^^^^^^^^
AttributeError: type object 'UserActivity' has no attribute 'type'
[2025-03-06 06:13:11] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:13:22] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 692, in index
    Client.last_tested <= one_month_ago
    ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'Client' has no attribute 'last_tested'
[2025-03-06 06:13:22] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:13:29] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 692, in index
    Client.last_tested <= one_month_ago
    ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'Client' has no attribute 'last_tested'
[2025-03-06 06:13:29] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:15:35] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 758, in index
    @app.template_filter('pluralize')
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "scaffold.py"), line 49, in wrapper_func
    self._check_setup_finished(f_name)
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 511, in _check_setup_finished
    raise AssertionError(
AssertionError: The setup method 'template_filter' can no longer be called on the application. It has already handled its first request, any changes will not be applied consistently.
Make sure all imports, decorators, functions, etc. needed to set up the application are done before running it.
[2025-03-06 06:15:35] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:16:09] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 758, in index
    @app.template_filter('pluralize')
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "scaffold.py"), line 49, in wrapper_func
    self._check_setup_finished(f_name)
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 511, in _check_setup_finished
    raise AssertionError(
AssertionError: The setup method 'template_filter' can no longer be called on the application. It has already handled its first request, any changes will not be applied consistently.
Make sure all imports, decorators, functions, etc. needed to set up the application are done before running it.
[2025-03-06 06:16:09] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:16:19] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 758, in index
    @app.template_filter('pluralize')
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "scaffold.py"), line 49, in wrapper_func
    self._check_setup_finished(f_name)
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 511, in _check_setup_finished
    raise AssertionError(
AssertionError: The setup method 'template_filter' can no longer be called on the application. It has already handled its first request, any changes will not be applied consistently.
Make sure all imports, decorators, functions, etc. needed to set up the application are done before running it.
[2025-03-06 06:16:19] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:16:27] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 758, in index
    @app.template_filter('pluralize')
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "scaffold.py"), line 49, in wrapper_func
    self._check_setup_finished(f_name)
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 511, in _check_setup_finished
    raise AssertionError(
AssertionError: The setup method 'template_filter' can no longer be called on the application. It has already handled its first request, any changes will not be applied consistently.
Make sure all imports, decorators, functions, etc. needed to set up the application are done before running it.
[2025-03-06 06:16:27] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:16:59] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 758, in index
    @app.template_filter('pluralize')
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "scaffold.py"), line 49, in wrapper_func
    self._check_setup_finished(f_name)
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 511, in _check_setup_finished
    raise AssertionError(
AssertionError: The setup method 'template_filter' can no longer be called on the application. It has already handled its first request, any changes will not be applied consistently.
Make sure all imports, decorators, functions, etc. needed to set up the application are done before running it.
[2025-03-06 06:16:59] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:28:56] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 683, in index
    if client.last_testing_date and client.last_testing_date.date() <= reminder_period:
       ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Client' object has no attribute 'last_testing_date'
[2025-03-06 06:28:56] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-06 06:32:55] ERROR в app: Exception on / [GET]
Traceback (most recent call last):
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask", "app.py"), line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("C:", "Users", "aslan", "AppData", "Local", "Packages", "PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0", "LocalCache", "local-packages", "Python311", "site-packages", "flask_login", "utils.py"), line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File os.path.join("D:", "Projects", "MedicalMind", "Final1.0", "app.py"), line 683, in index
    next_testing_date = last_testing_date + timedelta(days=30)
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Client' object has no attribute 'last_testing_date'
[2025-03-06 06:32:55] ERROR в app: Внутренняя ошибка сервера: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
[2025-03-08 07:26:25] ERROR в save_report: Ошибка при сохранении отчета: name 'soffice_path' is not defined
[2025-03-08 07:26:25] ERROR в save_report: Ошибка при автоматическом сохранении отчета: name 'soffice_path' is not defined
[2025-03-08 07:26:25] ERROR в save_report: Ошибка при сохранении отчета: name 'soffice_path' is not defined
[2025-03-08 07:28:00] ERROR в save_report: Ошибка при сохранении отчета: name 'soffice_path' is not defined
[2025-03-08 07:28:00] ERROR в save_report: Ошибка при автоматическом сохранении отчета: name 'soffice_path' is not defined
[2025-03-08 07:28:00] ERROR в save_report: Ошибка при сохранении отчета: name 'soffice_path' is not defined
[2025-03-08 07:32:12] ERROR в save_report: LibreOffice не найден в стандартных путях
[2025-03-08 07:32:12] ERROR в save_report: Ошибка при сохранении отчета: LibreOffice не найден
[2025-03-08 07:32:12] ERROR в save_report: Ошибка при автоматическом сохранении отчета: LibreOffice не найден
[2025-03-08 07:32:12] ERROR в save_report: Ошибка при сохранении отчета: LibreOffice не найден
[2025-03-08 07:35:14] ERROR в save_report: LibreOffice не найден в стандартных путях
[2025-03-08 07:35:14] ERROR в save_report: Ошибка при сохранении отчета: LibreOffice не найден
[2025-03-08 07:35:14] ERROR в save_report: Ошибка при автоматическом сохранении отчета: LibreOffice не найден
[2025-03-08 07:35:14] ERROR в save_report: Ошибка при сохранении отчета: LibreOffice не найден
[2025-03-08 07:37:08] ERROR в save_report: LibreOffice не найден в стандартных путях
[2025-03-08 07:37:08] ERROR в save_report: Ошибка при сохранении отчета: LibreOffice не найден
[2025-03-08 07:37:08] ERROR в save_report: Ошибка при автоматическом сохранении отчета: LibreOffice не найден
[2025-03-08 07:37:08] ERROR в save_report: Ошибка при сохранении отчета: LibreOffice не найден
[2025-03-08 07:39:56] ERROR в save_report: LibreOffice не найден в стандартных путях
[2025-03-08 07:39:56] ERROR в save_report: Ошибка при сохранении отчета: LibreOffice не найден
[2025-03-08 07:39:56] ERROR в save_report: Ошибка при автоматическом сохранении отчета: LibreOffice не найден
[2025-03-08 07:39:56] ERROR в save_report: Ошибка при сохранении отчета: LibreOffice не найден
[2025-03-12 01:43:17] ERROR в save_report: Не удалось создать копию PPTX и базовый PDF: [Errno 2] No such file or directory: os.path.join("d:", "Projects", "MedicalMind", "Final1.0", "reports", "Аташан_Изольда_Геннадьевна_11_03_202517-06.pptx")
[2025-03-12 01:49:42] ERROR в save_report: Ошибка при конвертации в PDF через PowerPoint: (-**********, 'Неопознанная ошибка', ('Presentation (unknown member) : Слайды, выбранные для печати, более не существуют. Выберите, пожалуйста, другие.', 'Microsoft PowerPoint', '', 0, None))
[2025-03-12 01:49:43] ERROR в app: Ошибка при скачивании отчета: 'UserStats' object has no attribute 'increment_downloads'
[2025-03-12 01:50:09] ERROR в save_report: Ошибка при конвертации в PDF через PowerPoint: (-**********, 'Неопознанная ошибка', ('Presentation (unknown member) : Слайды, выбранные для печати, более не существуют. Выберите, пожалуйста, другие.', 'Microsoft PowerPoint', '', 0, None))
[2025-03-12 01:50:10] ERROR в app: Ошибка при скачивании отчета: 'UserStats' object has no attribute 'increment_downloads'
[2025-03-14 00:34:42] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-14 00:34:42] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-14 00:34:42] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-14 00:34:42] ERROR в save_report: Ошибка при сохранении отчета: name 'fos' is not defined
[2025-03-14 00:34:42] ERROR в save_report: Ошибка при автоматическом сохранении отчета: name 'fos' is not defined
[2025-03-14 00:34:42] ERROR в save_report: Ошибка при сохранении отчета: name 'fos' is not defined
[2025-03-14 00:34:44] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-14 00:34:44] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-14 00:34:44] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-14 00:34:44] ERROR в save_report: Ошибка при сохранении отчета: name 'fos' is not defined
[2025-03-14 00:34:44] ERROR в save_report: Ошибка при автоматическом сохранении отчета: name 'fos' is not defined
[2025-03-14 00:34:44] ERROR в save_report: Ошибка при сохранении отчета: name 'fos' is not defined
[2025-03-14 00:35:01] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-14 00:35:01] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-14 00:35:01] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-14 00:35:01] ERROR в save_report: Ошибка при сохранении отчета: name 'fos' is not defined
[2025-03-14 00:35:01] ERROR в save_report: Ошибка при автоматическом сохранении отчета: name 'fos' is not defined
[2025-03-14 00:35:01] ERROR в save_report: Ошибка при сохранении отчета: name 'fos' is not defined
[2025-03-14 00:35:17] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-14 00:35:17] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-14 00:35:17] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:26:41] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:26:41] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:26:41] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:36:02] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:36:02] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:36:02] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:39:55] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:39:55] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:39:55] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:41:01] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:41:01] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:41:01] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:48:01] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:48:01] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:48:01] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:48:55] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:48:55] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:48:55] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:50:32] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:50:32] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:50:32] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:51:14] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:51:14] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:51:14] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:52:11] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:52:11] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:52:11] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:59:35] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:59:35] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-18 23:59:35] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 00:15:37] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 00:15:37] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 00:15:37] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:09:18] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:09:18] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:09:18] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:09:59] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:09:59] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:09:59] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:36:12] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:36:12] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:36:12] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:45:43] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:45:43] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:45:43] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:47:46] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:47:46] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:47:46] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:49:35] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:49:35] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-19 01:49:35] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 01:56:42] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 01:56:42] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 01:56:42] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 02:02:39] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 02:02:39] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 02:02:39] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 02:03:02] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 02:03:02] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 02:03:02] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 02:11:34] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 02:11:34] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 02:11:34] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 02:12:24] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 02:12:24] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 02:12:24] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:22:40] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:22:40] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:22:41] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:38:45] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:38:45] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:38:45] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:42:06] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:42:06] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:42:06] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:45:16] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:45:16] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:45:16] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:46:59] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:46:59] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:46:59] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:47:33] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:47:33] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:47:33] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:48:16] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:48:16] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:48:16] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:49:49] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:49:49] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:49:49] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:50:24] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:50:24] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:50:24] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:56:43] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:56:43] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:56:43] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:57:15] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:57:15] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:57:15] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:59:49] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:59:49] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 03:59:49] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 04:00:58] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 04:00:58] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 04:00:58] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 04:02:16] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 04:02:16] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-03-22 04:02:16] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-07-12 05:09:00] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-07-12 05:09:00] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-07-12 05:09:00] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-07-12 05:22:32] ERROR в save_report: Ошибка при инициализации API Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-07-12 05:22:32] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
[2025-07-12 05:22:32] ERROR в save_report: Ошибка при сохранении в Google Sheets: Expecting value: line 5 column 18 (char 152)
