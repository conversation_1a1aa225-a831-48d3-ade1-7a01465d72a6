import os
import datetime
import shutil
import sys
import collections
import importlib

# Создаем обертку вокруг импортов pptx, чтобы исправить проблемы совместимости
def patch_pptx_imports():
    # Монки-патч для collections.abc, если библиотека ожидает collections.Container
    if not hasattr(collections, 'Container') and hasattr(collections, 'abc') and hasattr(collections.abc, 'Container'):
        collections.Container = collections.abc.Container
    if not hasattr(collections, 'Mapping') and hasattr(collections, 'abc') and hasattr(collections.abc, 'Mapping'):
        collections.Mapping = collections.abc.Mapping
        
    # Если это не помогло, попробуем другой подход
    try:
        import pptx
        return True
    except (ImportError, AttributeError) as e:
        print(f"Ошибка при импорте pptx: {e}")
        return False

# Попытка патчить импорты
patch_success = patch_pptx_imports()

# Импортируем pptx только если патч был успешным
from pptx import Presentation
from pptx.util import Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.dml.color import RGBColor

# Импортируем кроссплатформенные библиотеки вместо win32com
import platform
import subprocess
import traceback
import logging
from google.oauth2 import service_account
from googleapiclient.discovery import build

# Настройка логирования
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# Маппинг категорий
mineral_mapping = {
    'Кальций': 'Ca', 'Железо': 'Fe', 'Цинк': 'Zn', 'Селен': 'Se',
    'Фосфор': 'P', 'Калий': 'K', 'Магний': 'Mg', 'Медь': 'Cu',
    'Кобальт': 'Co', 'Марганец': 'Mn', 'Йод': 'I', 'Никель': 'Ni',
    'Фтор': 'F', 'Молибден': 'Mo', 'Ванадий': 'V', 'Олово': 'Sn',
    'Кремний': 'Si', 'Стронций': 'Sr', 'Бор': 'B',
    # Добавляем варианты с символами
    'Ca': 'Ca', 'Fe': 'Fe', 'Zn': 'Zn', 'Se': 'Se',
    'P': 'P', 'K': 'K', 'Mg': 'Mg', 'Cu': 'Cu',
    'Co': 'Co', 'Mn': 'Mn', 'I': 'I', 'Ni': 'Ni',
    'F': 'F', 'Mo': 'Mo', 'V': 'V', 'Sn': 'Sn',
    'Si': 'Si', 'Sr': 'Sr', 'B': 'B'
}

vitamin_mapping = {
    'Витамин A': 'A', 'Витамин B1': 'B1', 'Витамин B2': 'B2', 'Витамин B3': 'B3',
    'Витамин B6': 'B6', 'Витамин B12': 'B12', 'Витамин C': 'C', 'Витамин D3': 'D3',
    'Витамин E': 'E', 'Витамин K': 'K',
    # Добавляем варианты без слова "Витамин"
    'A': 'A', 'B1': 'B1', 'B2': 'B2', 'B3': 'B3',
    'B6': 'B6', 'B12': 'B12', 'C': 'C', 'D3': 'D3',
    'E': 'E', 'K': 'K',
    # Добавляем варианты с "витамин" в скобках
    'A(витамин)': 'A', 'B1(витамин)': 'B1', 'B2(витамин)': 'B2', 'B3(витамин)': 'B3',
    'B6(витамин)': 'B6', 'B12(витамин)': 'B12', 'C(витамин)': 'C', 'D3(витамин)': 'D3',
    'E(витамин)': 'E', 'K(витамин)': 'K'
}

amino_acid_mapping = {
    'Лизин': 'Lys', 'Триптофан': 'Trp', 'Фенилаланин': 'Phe', 'Метионин': 'Met',
    'Треонин': 'Thr', 'Изолейцин': 'Ile', 'Лейцин': 'Leu', 'Валин': 'Val',
    'Гистидин': 'His', 'Аргинин': 'Arg',
    # Добавляем сокращенные варианты
    'Lys': 'Lys', 'Trp': 'Trp', 'Phe': 'Phe', 'Met': 'Met',
    'Thr': 'Thr', 'Ile': 'Ile', 'Leu': 'Leu', 'Val': 'Val',
    'His': 'His', 'Arg': 'Arg'
}

coenzyme_mapping = {
    'Никотинамид': 'NAM', 'Биотин': 'B7', 'Пантотеновая кислота': 'B5',
    'Фолиевая кислота': 'B9', 'Коэнзим Q10': 'CoQ10', 'Глутатион': 'GSH',
    # Добавляем варианты с сокращениями
    'NAM': 'NAM', 'B7': 'B7', 'B5': 'B5',
    'B9': 'B9', 'CoQ10': 'CoQ10', 'GSH': 'GSH',
    # Добавляем варианты написания
    'Коэнзим Q 10': 'CoQ10', 'Q10': 'CoQ10', 'Коэнзим Q-10': 'CoQ10'
}

fatty_acid_mapping = {
    'Липоевая кислота': 'LA', 'Альфа-линоленовая кислота': 'ALA',
    'Гамма-линоленовая кислота': 'GLA', 'Арахидоновая кислота': 'AA',
    # Добавляем сокращенные варианты
    'LA': 'LA', 'ALA': 'ALA', 'GLA': 'GLA', 'AA': 'AA',
    # Добавляем варианты без слова "кислота"
    'Липоевая': 'LA', 'Альфа-линоленовая': 'ALA',
    'Гамма-линоленовая': 'GLA', 'Арахидоновая': 'AA'
}

def save_to_pptx(file_path, patient_info, tree, get_element_value):
    try:
        # Настройка более подробного логирования
        log_file = os.path.join(os.path.dirname(__file__), "logs", "debug_log.txt")
        file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        logging.getLogger().addHandler(file_handler)
        
        logging.info("Входящие данные пациента: %s", patient_info)
        logging.info("Количество элементов в tree: %d", len(list(tree.get_children())))
        
        # Логирование всех элементов в tree
        logging.info("Содержимое tree:")
        for i in tree.get_children():
            values = tree.item(i)['values']
            logging.info("Элемент %s: %s", i, values)
        
        # Получение данных рекомендаций и несовместимых элементов
        recommendations_data = None
        incompatible_data = None
        
        for i in tree.get_children():
            values = tree.item(i)['values']
            logging.debug("Проверяем строку: %s", values)
            
            if values and len(values) > 0 and str(values[0]).strip() == "Несовместимые в курсе":
                logging.info("Найдены данные о несовместимых элементах: %s", values)
                incompatible_elements = []
                for j in range(1, 4):
                    if len(values) > j and values[j]:
                        incompatible_elements.append(str(values[j]).strip())
                incompatible_data = '; '.join(filter(None, incompatible_elements))
                logging.info("Подготовленные данные о несовместимых: %s", incompatible_data)
            
            elif values and len(values) > 0 and str(values[0]).strip() == "Рекомендации к курсу":
                logging.info("Найдены данные рекомендаций: %s", values)
                recommendations_data = {
                    'morning': str(values[1]).strip() if len(values) > 1 else '',
                    'day': str(values[2]).strip() if len(values) > 2 else '',
                    'evening': str(values[3]).strip() if len(values) > 3 else ''
                }
                logging.info("Подготовленные данные рекомендаций: %s", recommendations_data)
            
            if recommendations_data and incompatible_data:
                break

        # Проверка наличия данных
        if not recommendations_data or not incompatible_data:
            logging.warning("Данные рекомендаций и/или несовместимых элементов отсутствуют")

        # Инициализируем deficits_text по умолчанию
        deficits_text = ""

        # Функция для расчета ИМТ
        def calculate_bmi(body_type_str):
            """
            Рассчитывает ИМТ из строки телосложения в формате "150cm, 57kg"
            Возвращает строку с ИМТ и интерпретацией
            """
            try:
                if not body_type_str:
                    return "Данные не указаны"

                # Извлекаем рост и вес из строки
                import re
                height_match = re.search(r'(\d+(?:\.\d+)?)\s*cm', body_type_str, re.IGNORECASE)
                weight_match = re.search(r'(\d+(?:\.\d+)?)\s*kg', body_type_str, re.IGNORECASE)

                if not height_match or not weight_match:
                    return "Некорректные данные"

                height_cm = float(height_match.group(1))
                weight_kg = float(weight_match.group(1))

                # Переводим рост в метры
                height_m = height_cm / 100

                # Рассчитываем ИМТ
                bmi = weight_kg / (height_m ** 2)

                # Определяем интерпретацию ИМТ
                if bmi < 18.5:
                    interpretation = "Недостаточный вес"
                elif 18.5 <= bmi < 25:
                    interpretation = "Нормальный вес"
                elif 25 <= bmi < 30:
                    interpretation = "Избыточный вес"
                else:
                    interpretation = "Ожирение"

                return f"{bmi:.1f} ({interpretation})"

            except (ValueError, AttributeError) as e:
                logging.warning(f"Ошибка при расчете ИМТ: {e}")
                return "Ошибка расчета"

        # Рассчитываем ИМТ
        bmi_value = calculate_bmi(patient_info.get('body_type', ''))

        # Маркеры для текстовых полей
        markers = {
            '{{client_name}}': patient_info.get('name', patient_info.get('client_name', '')),
            '{{client_age}}': patient_info.get('age', patient_info.get('client_age', '')),
            '{{body_type}}': patient_info.get('body_type', ''),
            '{{test_date}}': patient_info.get('test_time', patient_info.get('test_date', '')).replace(os.path.join("r"), '').strip(),
            '{{gender}}': patient_info.get('gender', ''),
            '{{bmi_index}}': bmi_value
        }

        # Описание элементов для рекомендаций
        if recommendations_data:
            element_descriptions = {
                'Ca': 'Кальций необходим для поддержания здоровья костей и зубов, а также для нормального функционирования нервной системы и сердца. Участвует в процессах свертывания крови, сокращения мышц и передачи нервных импульсов.',
                'Fe': 'Железо является основным компонентом гемоглобина, который переносит кислород в крови. Также важен для иммунной системы и когнитивных функций. Недостаток железа может привести к анемии, усталости и снижению работоспособности.',
                'K': 'Калий - важнейший минерал и электролит, регулирующий водно-солевой баланс и кровяное давление. Необходим для нормальной работы сердца, передачи нервных импульсов и сокращения мышц. Дефицит может вызвать мышечную слабость и аритмию.',
                'Zn': 'Цинк поддерживает работу иммунной системы, участвует в синтезе ДНК, заживлении ран и нормальном росте и развитии. Необходим для нормального функционирования органов чувств, особенно вкуса и обоняния.',
                'Se': 'Селен действует как мощный антиоксидант, защищает клетки от повреждений и поддерживает функцию щитовидной железы. Способствует укреплению иммунитета и защите от окислительного стресса.',
                'P': 'Фосфор важен для формирования костей и зубов, участвует в энергетическом обмене и поддерживает функцию клеточных мембран. Является компонентом ДНК и РНК, участвует в передаче генетической информации.',
                'Mg': 'Магний поддерживает работу мышц и нервной системы, участвует в более чем 300 биохимических реакциях в организме. Важен для энергетического обмена, синтеза белков и поддержания здоровья костей.',
                'Cu': 'Медь участвует в образовании красных кровяных клеток, поддерживает иммунную систему и способствует усвоению железа. Необходима для формирования коллагена и эластина, важных компонентов соединительной ткани.',
                'Co': 'Кобальт важен для синтеза витамина B12, который необходим для образования красных кровяных клеток и нормальной работы нервной системы. Участвует в метаболизме жиров и белков.',
                'Mn': 'Марганец поддерживает здоровье костей и метаболизм, участвует в антиоксидантной защите и формировании соединительной ткани. Необходим для нормального функционирования мозга и нервной системы.',
                'I': 'Йод необходим для функции щитовидной железы, которая регулирует метаболизм и рост. Критически важен для нормального развития мозга и нервной системы, особенно во время беременности и раннего детства.',
                'Ni': 'Никель участвует в метаболизме и является кофактором для некоторых ферментов. Способствует усвоению железа и поддерживает структуру и функцию клеточных мембран.',
                'F': 'Фтор укрепляет зубы и кости, предотвращает развитие кариеса. Повышает устойчивость зубной эмали к воздействию кислот и бактерий, способствует реминерализации зубов.',
                'Mo': 'Молибден важен для метаболизма, участвует в работе ферментов, которые обрабатывают серу в организме. Необходим для детоксикации некоторых соединений и метаболизма пуринов.',
                'V': 'Ванадий регулирует уровень сахара в крови и участвует в метаболизме липидов. Может имитировать действие инсулина и улучшать чувствительность клеток к инсулину.',
                'Sn': 'Олово поддерживает обмен веществ и может играть роль в иммунной функции. Участвует в синтезе белков и метаболизме аминокислот.',
                'Si': 'Кремний укрепляет соединительные ткани, способствует здоровью костей, кожи, волос и ногтей. Важен для синтеза коллагена и формирования хрящевой ткани.',
                'Sr': 'Стронций поддерживает здоровье костей и может помочь в профилактике остеопороза. Способствует минерализации костной ткани и улучшает микроархитектуру костей.',
                'B': 'Бор способствует метаболизму кальция и магния, поддерживает здоровье костей и функцию мозга. Влияет на активность гормонов и может улучшать когнитивные функции.',
                'A': 'Витамин A важен для зрения и иммунитета, поддерживает здоровье кожи и слизистых оболочек. Необходим для нормального роста и развития, репродуктивной функции и формирования костей.',
                'B1': 'Витамин B1 (тиамин) поддерживает энергообмен, необходим для нормальной работы нервной системы и сердца. Участвует в метаболизме углеводов и функционировании мозга.',
                'B2': 'Витамин B2 (рибофлавин) важен для кожи и глаз, участвует в энергетическом обмене и антиоксидантной защите. Необходим для роста, развития и функционирования клеток.',
                'B3': 'Витамин B3 (ниацин) поддерживает здоровье кожи и нервной системы, участвует в метаболизме энергии. Способствует снижению уровня холестерина и улучшению кровообращения.',
                'B6': 'Витамин B6 важен для мозга и кроветворения, участвует в синтезе нейротрансмиттеров и метаболизме белков. Поддерживает иммунную функцию и регулирует уровень гомоцистеина.',
                'B12': 'Витамин B12 необходим для нервной системы и крови, участвует в синтезе ДНК и образовании красных кровяных клеток. Критически важен для неврологического здоровья и когнитивных функций.',
                'C': 'Витамин C укрепляет иммунитет и антиоксидантную защиту, способствует усвоению железа и образованию коллагена. Ускоряет заживление ран и поддерживает здоровье кровеносных сосудов.',
                'D3': 'Витамин D3 важен для здоровья костей, иммунной системы и усвоения кальция и фосфора. Регулирует множество генов и может снижать риск хронических заболеваний.',
                'E': 'Витамин E действует как антиоксидант, защищает клетки от повреждений и поддерживает иммунную функцию. Важен для здоровья кожи, глаз и репродуктивной системы.',
                'K': 'Витамин K играет ключевую роль в процессах свертывания крови. Необходим для синтеза белков, участвующих в формировании костной ткани. Способствует профилактике остеопороза и поддержанию здоровья сосудов.',
                'Lys': 'Лизин поддерживает рост и восстановление тканей, участвует в производстве антител и гормонов. Необходим для усвоения кальция и формирования коллагена, важен для здоровья кожи.',
                'Trp': 'Триптофан важен для производства серотонина, который регулирует настроение, сон и аппетит. Является предшественником мелатонина и ниацина, влияет на иммунную функцию.',
                'Phe': 'Фенилаланин участвует в синтезе нейротрансмиттеров, влияющих на настроение и когнитивные функции. Является предшественником тирозина, адреналина и норадреналина.',
                'Met': 'Метионин поддерживает детоксикацию и метаболизм, участвует в синтезе белков и антиоксидантной защите. Важен для здоровья печени и является источником серы для организма.',
                'Thr': 'Треонин важен для синтеза белков, поддерживает иммунную систему и здоровье печени. Участвует в формировании коллагена и эластина, необходим для здоровья кожи и соединительной ткани.',
                'Ile': 'Изолейцин поддерживает мышцы и энергообмен, регулирует уровень сахара в крови. Необходим для заживления тканей и стимуляции выработки гемоглобина.',
                'Leu': 'Лейцин способствует восстановлению мышц, регулирует уровень сахара в крови и стимулирует заживление ран. Играет ключевую роль в синтезе белка и росте мышечной ткани.',
                'Val': 'Валин важен для роста и восстановления тканей, поддерживает энергетический обмен и когнитивные функции. Необходим для нормального функционирования мышц и нервной системы.',
                'His': 'Гистидин поддерживает иммунную систему, участвует в росте и восстановлении тканей, важен для миелиновой оболочки нервов. Является предшественником гистамина, участвующего в иммунных и воспалительных реакциях.',
                'Arg': 'Аргинин важен для кровообращения, иммунной функции и заживления ран. Стимулирует выработку оксида азота, расширяющего кровеносные сосуды, и способствует выработке гормона роста.',
                'NAM': 'Никотинамид поддерживает метаболизм энергии, участвует в репарации ДНК и функционировании нервной системы. Обладает противовоспалительными свойствами и может защищать от некоторых видов рака.',
                'B7': 'Биотин важен для здоровья кожи и волос, участвует в метаболизме жиров и углеводов. Необходим для нормального функционирования нервной системы и поддержания здорового уровня сахара в крови.',
                'B5': 'Пантотеновая кислота поддерживает метаболизм, участвует в синтезе гормонов и холестерина. Необходима для производства коэнзима А, ключевого компонента в метаболизме энергии.',
                'B9': 'Фолиевая кислота необходима для ДНК и кроветворения, особенно важна во время беременности для предотвращения дефектов нервной трубки. Участвует в синтезе аминокислот и формировании красных кровяных клеток.',
                'CoQ10': 'Коэнзим Q10 действует как антиоксидант, поддерживает производство энергии в клетках и здоровье сердца. Особенно важен для тканей с высокими энергетическими потребностями, таких как сердце и мозг.',
                'GSH': 'Глутатион защищает клетки от окислительного стресса, поддерживает иммунную функцию и детоксикацию. Является ключевым антиоксидантом, участвующим в нейтрализации свободных радикалов и токсинов.',
                'LA': 'Липоевая кислота поддерживает антиоксидантную защиту, участвует в энергетическом метаболизме и детоксикации. Способствует регенерации других антиоксидантов и может улучшать чувствительность к инсулину.',
                'ALA': 'Альфа-линоленовая кислота укрепляет метаболизм, поддерживает здоровье сердца и противовоспалительные процессы. Является предшественником омега-3 жирных кислот, важных для мозга и сердечно-сосудистой системы.',
                'GLA': 'Гамма-линоленовая кислота поддерживает кожу и иммунную систему, обладает противовоспалительными свойствами. Может помогать при предменструальном синдроме, артрите и других воспалительных состояниях.',
                'AA': 'Арахидоновая кислота важна для мозга и мышц, участвует в воспалительных и иммунных процессах. Является предшественником эйкозаноидов, регулирующих воспаление, свертывание крови и иммунный ответ.',
                'Коэнзим Q10': 'Коэнзим Q10 действует как антиоксидант, участвует в производстве энергии в клетках, поддерживает здоровье сердца и замедляет процессы старения.'
            }

            def replace_element_with_description(text):
                if not text:
                    return ""
                
                logging.info(f"Начало обработки текста: {text}")
                
                # Расширенный маппинг форматов
                format_mapping = {
                    'K(калий)': 'K',
                    'K (калий)': 'K',
                    'K(Калий)': 'K',
                    'K (Калий)': 'K',
                    'Калий': 'K',
                    'K': 'K',
                    'K(витамин)': 'K',
                    'K (витамин)': 'K',
                    'K(Витамин)': 'K',
                    'K (Витамин)': 'K',
                    'Витамин K': 'K',
                    'Витамин C': 'C',
                    'Витамин B3': 'B3',
                    'Витамин E': 'E',
                    'CoQ10': 'Коэнзим Q10',
                    'Коэнзим Q10': 'Коэнзим Q10',
                    'Si': 'Si',
                    'C': 'C',
                    'B3': 'B3',
                    'E': 'E',
                    'Кремний': 'Si',
                    'Витамин B3 (ниацин)': 'B3',
                    'B3 (ниацин)': 'B3'
                }
                
                descriptions = []
                processed_elements = set()
                
                # Разбиваем текст на элементы
                elements = [e.strip() for e in text.split(';')]
                logging.info(f"Разбитые элементы: {elements}")
                
                for element in elements:
                    if not element:
                        continue
                        
                    original_element = element.strip()
                    logging.info(f"Обработка элемента: {original_element}")
                    
                    # Создаем все возможные варианты написания элемента
                    element_variants = {original_element}
                    
                    # Сначала проверяем точное совпадение для калия
                    if original_element in ['K(калий)', 'K (калий)', 'Калий', 'K']:
                        description = 'Калий - важнейший минерал и электролит, регулирующий водно-солевой баланс и кровяное давление. Необходим для нормальной работы сердца, передачи нервных импульсов и сокращения мышц. Дефицит может вызвать мышечную слабость и аритмию.'
                        used_variant = 'K'
                        formatted_description = f"{original_element}: {description}"
                        descriptions.append(formatted_description)
                        processed_elements.add(used_variant)
                        logging.info(f"Найдено описание для калия: {original_element}")
                        continue

                    # Затем проверяем точное совпадение для витамина K
                    if original_element in ['K(витамин)', 'K (витамин)', 'Витамин K', 'K(Витамин)', 'K (Витамин)']:
                        description = 'Витамин K играет ключевую роль в процессах свертывания крови. Необходим для синтеза белков, участвующих в формировании костной ткани. Способствует профилактике остеопороза и поддержанию здоровья сосудов.'
                        used_variant = 'K'
                        formatted_description = f"{original_element}: {description}"
                        descriptions.append(formatted_description)
                        processed_elements.add(used_variant)
                        logging.info(f"Найдено описание для витамина K: {original_element}")
                        continue

                    # Если элемент содержит скобки, извлекаем базовый элемент и содержимое
                    if '(' in original_element and ')' in original_element:
                        base = original_element.split('(')[0].strip()
                        content = original_element.split('(')[1].split(')')[0].strip().lower()
                        
                        if content == 'калий':
                            element_variants = {'K'}  # Для калия используем только 'K'
                            logging.info(f"Найден калий в скобках, используем вариант 'K'")
                        elif content == 'витамин':
                            element_variants = {'K(витамин)'}  # Для витамина K используем специальный формат
                            logging.info(f"Найден витамин в скобках, используем вариант 'K(витамин)'")
                    # Проверяем маппинг форматов только если не обработали скобки
                    elif original_element in format_mapping:
                        mapped_element = format_mapping[original_element]
                        element_variants.add(mapped_element)
                        logging.info(f"Элемент {original_element} преобразован в {mapped_element}")
                    
                    logging.info(f"Варианты элемента {original_element}: {element_variants}")
                    
                    # Ищем описание для каждого варианта
                    description = None
                    used_variant = None
                    
                    # Проверяем, является ли элемент калием
                    if 'K' in element_variants and any('калий' in v.lower() for v in element_variants):
                        description = element_descriptions['K']
                        used_variant = 'K'
                        logging.info(f"Найдено описание для калия")
                    # Проверяем, является ли элемент витамином K
                    elif 'K' in element_variants and (any('витамин' in v.lower() for v in element_variants) or original_element == 'K'):
                        description = element_descriptions['K']  # Используем описание для витамина K
                        used_variant = 'K'
                        logging.info(f"Найдено описание для витамина K")
                    # Если не нашли, проверяем минералы
                    if not description:
                        for variant in element_variants:
                            if variant in mineral_mapping and mineral_mapping[variant] in element_descriptions:
                                description = element_descriptions[mineral_mapping[variant]]
                                used_variant = mineral_mapping[variant]
                                break
                    
                    # Если все еще не нашли, проверяем прямое совпадение в element_descriptions
                    if not description:
                        for variant in element_variants:
                            if variant in element_descriptions:
                                description = element_descriptions[variant]
                                used_variant = variant
                                break
                    
                    # Если все еще не нашли, проверяем витамины
                    if not description:
                        for variant in element_variants:
                            if variant in vitamin_mapping and vitamin_mapping[variant] in element_descriptions:
                                description = element_descriptions[vitamin_mapping[variant]]
                                used_variant = vitamin_mapping[variant]
                                break
                    
                    # И наконец коферменты
                    if not description:
                        for variant in element_variants:
                            if variant in coenzyme_mapping and coenzyme_mapping[variant] in element_descriptions:
                                description = element_descriptions[coenzyme_mapping[variant]]
                                used_variant = coenzyme_mapping[variant]
                                break
                    
                    if description and used_variant not in processed_elements:
                        formatted_description = f"{original_element}: {description}"
                        descriptions.append(formatted_description)
                        processed_elements.add(used_variant)
                        logging.info(f"Найдено описание для {original_element} через вариант {used_variant}")
                    else:
                        logging.error(f"Не найдено описание для элемента {original_element} (варианты: {element_variants})")
                
                if not descriptions:
                    logging.error(f"Не найдено ни одного описания для текста: {text}")
                    return ""
                
                result = "\n\n".join(descriptions)
                logging.info(f"Итоговые описания:\n{result}")
                return result

            morning_desc = replace_element_with_description(recommendations_data['morning'])
            day_desc = replace_element_with_description(recommendations_data['day'])
            evening_desc = replace_element_with_description(recommendations_data['evening'])

            deficits_text = "\n\n".join(filter(None, [morning_desc, day_desc, evening_desc]))
            deficits_text = fix_line_breaks(deficits_text)
            
            markers.update({
                '{{morning}}': recommendations_data['morning'],
                '{{day}}': recommendations_data['day'],
                '{{evening}}': recommendations_data['evening'],
                '{{deficits}}': deficits_text,
                '{{incompatible}}': incompatible_data or ''
            })

        # Собираем данные для всех категорий
        data_by_category = {
            'minerals': {}, 'vitamins': {}, 'amino_acids': {}, 'coenzymes': {}, 'fatty_acids': {}
        }
        
        # Переменная для отслеживания наличия калия K
        found_k = False
        
        for item in tree.get_children():
            values = tree.item(item)['values']
            if values and len(values) > 6:
                element_name = values[0]
                index = values[6]
                logging.debug(f"Элемент: {element_name}, индекс: {index}")
                if index is not None:
                    try:
                        index_num = int(float(index))
                        table_value = f"-{abs(index_num)}" if index_num < 0 else str(index_num)
                    except (ValueError, TypeError):
                        logging.warning(f"Некорректное значение индекса для элемента {element_name}: {index}")
                        table_value = ''
                    
                    # Специальная обработка для K
                    if element_name in ["K", "Калий", "K(калий)"]:
                        data_by_category['minerals']['K'] = {'value': table_value}
                        logging.info(f"Добавлен минерал: K = {table_value}")
                        # Отмечаем, что мы нашли K
                        found_k = True
                        # Если это просто K без уточнения, добавляем его также в витамины
                        if element_name == "K":
                            data_by_category['vitamins']['K'] = {'value': table_value}
                            logging.info(f"Добавлен витамин: K = {table_value} (дублирование из K)")
                    elif element_name in ["Витамин K", "K(витамин)"]:
                        data_by_category['vitamins']['K'] = {'value': table_value}
                        logging.info(f"Добавлен витамин: K = {table_value}")
                    elif element_name in mineral_mapping:
                        data_by_category['minerals'][mineral_mapping[element_name]] = {'value': table_value}
                        logging.info(f"Добавлен минерал: {mineral_mapping[element_name]} = {table_value}")
                    elif element_name in vitamin_mapping:
                        data_by_category['vitamins'][vitamin_mapping[element_name]] = {'value': table_value}
                        logging.info(f"Добавлен витамин: {vitamin_mapping[element_name]} = {table_value}")
                    elif element_name in amino_acid_mapping:
                        data_by_category['amino_acids'][amino_acid_mapping[element_name]] = {'value': table_value}
                        logging.info(f"Добавлена аминокислота: {amino_acid_mapping[element_name]} = {table_value}")
                    elif element_name in coenzyme_mapping:
                        data_by_category['coenzymes'][coenzyme_mapping[element_name]] = {'value': table_value}
                        logging.info(f"Добавлен кофермент: {coenzyme_mapping[element_name]} = {table_value}")
                    elif element_name in fatty_acid_mapping:
                        data_by_category['fatty_acids'][fatty_acid_mapping[element_name]] = {'value': table_value}
                        logging.info(f"Добавлена жирная кислота: {fatty_acid_mapping[element_name]} = {table_value}")
                    else:
                        logging.warning(f"Неизвестный элемент: {element_name}")
        
        # Если мы нашли K, и он только в минералах, добавляем его также в витамины
        if found_k and 'K' in data_by_category['minerals'] and 'K' not in data_by_category['vitamins']:
            data_by_category['vitamins']['K'] = data_by_category['minerals']['K']
            logging.info(f"Добавлен витамин K из минерала K: {data_by_category['minerals']['K']['value']}")

        # Проверка наличия данных в категориях
        for category, data in data_by_category.items():
            logging.info(f"Данные в категории {category}: {len(data)} элементов")

        # Проверка шаблона
        template_path = os.path.join(os.path.dirname(__file__), 'main_template_new.pptx')
        if not os.path.exists(template_path):
            # Если новый шаблон не найден, используем старый
            template_path = os.path.join(os.path.dirname(__file__), 'main_template.pptx')
            if not os.path.exists(template_path):
                raise FileNotFoundError(f"Template file not found: {template_path}")

        shutil.copy2(template_path, file_path)
        logging.info(f"Template copied to: {file_path}")

        presentation = Presentation(file_path)
        
        def center_text_in_cell(cell):
            for paragraph in cell.text_frame.paragraphs:
                paragraph.alignment = PP_ALIGN.CENTER
                for run in paragraph.runs:
                    run.font.size = Pt(10)
                    run.font.color.rgb = RGBColor(0, 0, 0)

        # Функция для раскраски ячеек таблицы в зависимости от значения
        def apply_cell_color(cell, value):
            try:
                # Конвертируем значение в число, сохраняя знак
                num_value = int(value)
                
                # Определяем цвет в зависимости от значения
                if num_value == -3:
                    cell.fill.solid()
                    cell.fill.fore_color.rgb = RGBColor(192, 0, 0)  # Темно-красный
                elif num_value == -2 or num_value == 3:
                    cell.fill.solid()
                    cell.fill.fore_color.rgb = RGBColor(255, 0, 0)  # Красный
                elif num_value in [-1, 1, 2]:
                    cell.fill.solid()
                    cell.fill.fore_color.rgb = RGBColor(255, 255, 0)  # Желтый
                elif num_value == 0:
                    cell.fill.solid()
                    cell.fill.fore_color.rgb = RGBColor(146, 208, 80)  # Зеленый
            except (ValueError, TypeError):
                # Если не удалось преобразовать значение в число, оставляем ячейку без цвета
                pass

        # Обрабатываем все слайды
        for slide_index, slide in enumerate(presentation.slides, 1):
            logging.info(f"Processing slide {slide_index}")
            for shape in slide.shapes:
                # Обработка таблиц
                if shape.has_table:
                    table = shape.table
                    first_cell = table.cell(0, 0).text.strip()
                    logging.info(f"Found table on slide {slide_index} with header: {first_cell}")
                    
                    if first_cell == "ФИО:":
                        # Обработка таблицы с информацией о пациенте
                        for row_idx, row_label in enumerate(["ФИО:", "Возраст:", "Телосложение:", "Дата и время:"]):
                            if row_idx < len(table.rows):
                                cell = table.cell(row_idx, 1)
                                if row_label == "ФИО:":
                                    cell.text = patient_info.get('name', '')
                                elif row_label == "Возраст:":
                                    cell.text = patient_info.get('age', '')
                                elif row_label == "Телосложение:":
                                    cell.text = patient_info.get('body_type', '')
                                elif row_label == "Дата и время:":
                                    cell.text = patient_info.get('test_time', '')
                                logging.info(f"Заполнена ячейка {row_label} значением {cell.text}")
                                
                                # Особое форматирование для информации о клиенте
                                for paragraph in cell.text_frame.paragraphs:
                                    paragraph.alignment = PP_ALIGN.LEFT  # Выравнивание по левому краю
                                    paragraph.vertical_anchor = MSO_ANCHOR.MIDDLE  # Вертикальное выравнивание по центру
                                    for run in paragraph.runs:
                                        run.font.size = Pt(14)  # Больший шрифт только для информации о клиенте
                                        run.font.color.rgb = RGBColor(0, 0, 0)
                        
                        # Добавляем границы для улучшения сетки
                        for row in range(len(table.rows)):
                            for col in range(len(table.columns)):
                                cell = table.cell(row, col)
                                # В библиотеке python-pptx нет прямого доступа к границам через borders
                                # Устанавливаем общие свойства ячейки вместо этого
                                cell.fill.solid()
                                cell.fill.fore_color.rgb = RGBColor(255, 255, 255)  # Белый фон
                    
                    elif "Состояние баланса минерал" in first_cell:
                        for col in range(1, len(table.columns)):
                            element = table.cell(0, col).text.strip()
                            if element in data_by_category['minerals']:
                                cell = table.cell(1, col)
                                cell.text = data_by_category['minerals'][element]['value']
                                center_text_in_cell(cell)
                                # Добавляем раскраску ячейки
                                apply_cell_color(cell, cell.text)
                    
                    # Обработка таблицы витаминов
                    elif any(x in first_cell for x in ["Состояние баланса витамин", "Витамины", "Состояние баланса витаминов"]):
                        logging.info(f"Обрабатываем таблицу витаминов: {first_cell}")
                        
                        # Если нет данных о витаминах, попробуем создать их из других категорий
                        if not data_by_category['vitamins']:
                            logging.info("Нет данных о витаминах, пытаемся создать их из других категорий")
                            # Проверяем все элементы в дереве
                            for item in tree.get_children():
                                values = tree.item(item)['values']
                                if values and len(values) > 6:
                                    element_name = values[0]
                                    index = values[6]
                                    # Проверяем, содержит ли название элемента ключевые слова витаминов
                                    for vitamin_key in ['A', 'B1', 'B2', 'B3', 'B6', 'B12', 'C', 'D3', 'E', 'K']:
                                        if vitamin_key in element_name:
                                            try:
                                                index_num = int(float(index))
                                                table_value = f"-{abs(index_num)}" if index_num < 0 else str(index_num)
                                                data_by_category['vitamins'][vitamin_key] = {'value': table_value}
                                                logging.info(f"Добавлен витамин из дополнительного поиска: {vitamin_key} = {table_value}")
                                            except (ValueError, TypeError):
                                                logging.warning(f"Некорректное значение индекса для элемента {element_name}: {index}")
                        
                        for col in range(1, len(table.columns)):
                            element = table.cell(0, col).text.strip()
                            logging.info(f"Проверяем элемент витамина: {element}")
                            if element in data_by_category['vitamins']:
                                cell = table.cell(1, col)
                                cell.text = data_by_category['vitamins'][element]['value']
                                center_text_in_cell(cell)
                                # Добавляем раскраску ячейки
                                apply_cell_color(cell, cell.text)
                                logging.info(f"Заполнена ячейка витамина {element} значением {cell.text}")
                    
                    # Обработка таблицы аминокислот
                    elif any(x in first_cell for x in ["Состояние баланса аминокислот", "Аминокислоты"]):
                        logging.info(f"Обрабатываем таблицу аминокислот: {first_cell}")
                        for col in range(1, len(table.columns)):
                            element = table.cell(0, col).text.strip()
                            logging.info(f"Проверяем элемент аминокислоты: {element}")
                            if element in data_by_category['amino_acids']:
                                cell = table.cell(1, col)
                                cell.text = data_by_category['amino_acids'][element]['value']
                                center_text_in_cell(cell)
                                # Добавляем раскраску ячейки
                                apply_cell_color(cell, cell.text)
                                logging.info(f"Заполнена ячейка аминокислоты {element} значением {cell.text}")
                    
                    # Обработка таблицы коферментов
                    elif any(x in first_cell for x in ["Состояние баланса кофермент", "Коферменты"]):
                        logging.info(f"Обрабатываем таблицу коферментов: {first_cell}")
                        for col in range(1, len(table.columns)):
                            element = table.cell(0, col).text.strip()
                            logging.info(f"Проверяем элемент кофермента: {element}")
                            if element in data_by_category['coenzymes']:
                                cell = table.cell(1, col)
                                cell.text = data_by_category['coenzymes'][element]['value']
                                center_text_in_cell(cell)
                                # Добавляем раскраску ячейки
                                apply_cell_color(cell, cell.text)
                                logging.info(f"Заполнена ячейка кофермента {element} значением {cell.text}")
                    
                    # Обработка таблицы жирных кислот
                    elif any(x in first_cell for x in ["Состояние баланса жирных кислот", "Жирные кислоты", "Состояние баланса жирных кислот:"]):
                        logging.info(f"Обрабатываем таблицу жирных кислот: {first_cell}")
                        
                        # Если мало данных о жирных кислотах, попробуем создать их из других категорий
                        if len(data_by_category['fatty_acids']) < 3:
                            logging.info("Мало данных о жирных кислотах, пытаемся создать их из других категорий")
                            # Проверяем все элементы в дереве
                            for item in tree.get_children():
                                values = tree.item(item)['values']
                                if values and len(values) > 6:
                                    element_name = values[0]
                                    index = values[6]
                                    # Проверяем, содержит ли название элемента ключевые слова жирных кислот
                                    for fa_key, fa_abbr in [
                                        ('Липоевая', 'LA'), 
                                        ('Альфа-линоленовая', 'ALA'), 
                                        ('Гамма-линоленовая', 'GLA'),
                                        ('Арахидоновая', 'AA')
                                    ]:
                                        if fa_key in element_name:
                                            try:
                                                index_num = int(float(index))
                                                table_value = f"-{abs(index_num)}" if index_num < 0 else str(index_num)
                                                data_by_category['fatty_acids'][fa_abbr] = {'value': table_value}
                                                logging.info(f"Добавлена жирная кислота из дополнительного поиска: {fa_abbr} = {table_value}")
                                            except (ValueError, TypeError):
                                                logging.warning(f"Некорректное значение индекса для элемента {element_name}: {index}")
                        
                        for col in range(1, len(table.columns)):
                            element = table.cell(0, col).text.strip()
                            logging.info(f"Проверяем элемент жирной кислоты: {element}")
                            if element in data_by_category['fatty_acids']:
                                cell = table.cell(1, col)
                                cell.text = data_by_category['fatty_acids'][element]['value']
                                center_text_in_cell(cell)
                                # Добавляем раскраску ячейки
                                apply_cell_color(cell, cell.text)
                                logging.info(f"Заполнена ячейка жирной кислоты {element} значением {cell.text}")
                    
                    
                    elif "Микроэлементы" in first_cell:
                        for col in range(1, len(table.columns)):
                            element = table.cell(0, col).text.strip()
                            if element in data_by_category['minerals']:
                                cell = table.cell(1, col)
                                cell.text = data_by_category['minerals'][element]['value']
                                center_text_in_cell(cell)
                                # Добавляем раскраску ячейки
                                apply_cell_color(cell, cell.text)
                    
                    elif first_cell == "Утро" and recommendations_data:
                        for idx, value in enumerate([
                            recommendations_data['morning'],
                            recommendations_data['day'],
                            recommendations_data['evening']
                        ]):
                            cell = table.cell(idx, 1)
                            text_frame = cell.text_frame
                            text_frame.clear()
                            p = text_frame.paragraphs[0]
                            p.alignment = PP_ALIGN.LEFT
                            run = p.add_run()
                            run.text = value
                            run.font.size = Pt(11)
                            run.font.bold = True  # Добавляем выделение жирным для всех ячеек
                            run.font.color.rgb = RGBColor(0, 0, 0)
                    
                    elif first_cell == "{{incompatible}}":
                        cell = table.cell(0, 0)
                        text_frame = cell.text_frame
                        text_frame.clear()
                        p = text_frame.paragraphs[0]
                        p.alignment = PP_ALIGN.LEFT
                        run = p.add_run()
                        run.text = incompatible_data or ''
                        run.font.size = Pt(11)
                        run.font.color.rgb = RGBColor(0, 0, 0)
                    
                    elif first_cell == "{{deficits}}" and deficits_text:
                        cell = table.cell(0, 0)
                        text_frame = cell.text_frame
                        text_frame.clear()
                        p = text_frame.paragraphs[0]
                        p.alignment = PP_ALIGN.LEFT
                        run = p.add_run()
                        run.text = deficits_text
                        run.font.size = Pt(11)
                        run.font.color.rgb = RGBColor(0, 0, 0)
                
                # Обработка текстовых полей с маркерами
                elif shape.has_text_frame:
                    text_frame = shape.text_frame
                    for paragraph in text_frame.paragraphs:
                        text = paragraph.text
                        original_text = text
                        for marker, value in markers.items():
                            if marker in text:
                                logging.info(f"Replacing {marker} with {value} on slide {slide_index}")
                                text = text.replace(marker, str(value))
                        
                        if text != original_text:
                            paragraph.text = text
                            for run in paragraph.runs:
                                run.font.size = Pt(11)
                                run.font.color.rgb = RGBColor(0, 0, 0)

        presentation.save(file_path)
        logging.info(f"PPTX saved: {file_path}")
        
        # Кроссплатформенное решение для конвертации PPTX в PDF
        pdf_path = file_path.replace('.pptx', '.pdf')
        
        # Определяем операционную систему
        current_os = platform.system()
        
        conversion_success = False
        
        # Для Windows используем LibreOffice или MS Office, если доступны
        if current_os == 'Windows':
            try:
                # Сначала пробуем использовать win32com, если доступен
                import pythoncom
                from win32com import client
                
                pythoncom.CoInitialize()
                powerpoint = client.Dispatch("Powerpoint.Application")
                deck = powerpoint.Presentations.Open(os.path.abspath(file_path))
                deck.SaveAs(os.path.abspath(pdf_path), 32)
                deck.Close()
                powerpoint.Quit()
                pythoncom.CoUninitialize()
                conversion_success = True
                logging.info("Конвертация в PDF выполнена с помощью MS PowerPoint")
            except Exception as e:
                logging.warning(f"Не удалось использовать MS PowerPoint: {str(e)}")
                # Пробуем LibreOffice
                try:
                    soffice_paths = [
                        ros.path.join("C:", "Program Files", "LibreOffice", "program", "soffice.exe"),
                        r"C:\Program Files (x86)\LibreOffice\program\soffice.exe"
                    ]
                    
                    for soffice_path in soffice_paths:
                        if os.path.exists(soffice_path):
                            cmd = [soffice_path, '--headless', '--convert-to', 'pdf', '--outdir', 
                                   os.path.dirname(file_path), file_path]
                            subprocess.run(cmd, check=True)
                            conversion_success = True
                            logging.info("Конвертация в PDF выполнена с помощью LibreOffice")
                            break
                except Exception as libreoffice_error:
                    logging.warning(f"Не удалось использовать LibreOffice: {str(libreoffice_error)}")
        
        # Для Linux используем LibreOffice
        elif current_os == 'Linux':
            try:
                cmd = ['libreoffice', '--headless', '--convert-to', 'pdf', '--outdir', 
                       os.path.dirname(file_path), file_path]
                subprocess.run(cmd, check=True)
                conversion_success = True
                logging.info("Конвертация в PDF выполнена с помощью LibreOffice")
            except Exception as e:
                logging.warning(f"Не удалось использовать LibreOffice: {str(e)}")
        
        # Для macOS используем LibreOffice или встроенные инструменты
        elif current_os == 'Darwin':  # macOS
            try:
                # Сначала пробуем LibreOffice
                libreoffice_paths = [
                    '/Applications/LibreOffice.app/Contents/MacOS/soffice',
                    '/Applications/LibreOffice.app/Contents/MacOS/libreoffice'
                ]
                
                for libreoffice_path in libreoffice_paths:
                    if os.path.exists(libreoffice_path):
                        cmd = [libreoffice_path, '--headless', '--convert-to', 'pdf', '--outdir', 
                               os.path.dirname(file_path), file_path]
                        subprocess.run(cmd, check=True)
                        conversion_success = True
                        logging.info("Конвертация в PDF выполнена с помощью LibreOffice")
                        break
                
                # Если LibreOffice не доступен, пробуем использовать встроенные инструменты macOS
                if not conversion_success:
                    cmd = ['/usr/bin/textutil', '-convert', 'pdf', '-output', pdf_path, file_path]
                    subprocess.run(cmd, check=True)
                    conversion_success = True
                    logging.info("Конвертация в PDF выполнена с помощью textutil")
            except Exception as e:
                logging.warning(f"Не удалось конвертировать в PDF на macOS: {str(e)}")
        
        # Если не удалось конвертировать в PDF, оставляем только PPTX
        if not conversion_success:
            logging.warning("Не удалось конвертировать PPTX в PDF. Файл сохранен только в формате PPTX.")
            return True
        
        try:
            os.remove(file_path)
            logging.info(f"Temporary PPTX removed: {file_path}")
        except Exception as e:
            logging.warning(f"Failed to remove temporary PPTX: {str(e)}")
        
        logging.info(f"Report saved as PDF: {pdf_path}")
        return True
                
    except Exception as e:
        logging.error("Ошибка при сохранении отчета: %s", str(e))
        traceback.print_exc()
        raise

def save_to_google_sheets(patient_info, tree, context):
    try:
        # Получаем имя пользователя из контекста
        username = context.get('username', 'Unknown')
        
        # Получаем текущую дату и время
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Формируем строку для записи в таблицу
        row = [
            username,  # Имя пользователя
            current_time,  # Дата и время сохранения
            patient_info.get('name', ''),  # Имя пациента
            patient_info.get('age', ''),  # Возраст
            patient_info.get('gender', ''),  # Пол
            patient_info.get('body_type', ''),  # Телосложение
            patient_info.get('test_time', '')  # Время тестирования
        ]

        # Используем ID таблицы из переменной окружения или значение по умолчанию
        SPREADSHEET_ID = os.environ.get('GOOGLE_SPREADSHEET_ID', '1v_dRbpRnrEwKOWWmGo8svI3IjdIKJAYmmYEYXNBt4ro')
        script_dir = os.path.dirname(os.path.abspath(__file__))
        possible_paths = [
            os.path.join(script_dir, 'service_account.json'),
            os.path.join(script_dir, 'credentials', 'service_account.json'),
            os.path.join(os.path.dirname(script_dir), 'service_account.json'),
        ]
        
        credentials_file = None
        for path in possible_paths:
            if os.path.exists(path):
                credentials_file = path
                logging.info(f"Найден файл учетных данных: {path}")
                break
                
        if credentials_file is None:
            logging.error("Файл service_account.json не найден. Запись в Google Sheets невозможна.")
            raise FileNotFoundError("service_account.json not found")
        
        SCOPES = ['https://www.googleapis.com/auth/spreadsheets']
        
        try:
            credentials = service_account.Credentials.from_service_account_file(credentials_file, scopes=SCOPES)
            service = build('sheets', 'v4', credentials=credentials)
            logging.info("Успешно создан сервис Google Sheets API")
        except Exception as e:
            logging.error(f"Ошибка при инициализации API Google Sheets: {str(e)}")
            raise
        
        try:
            result = service.spreadsheets().values().get(spreadsheetId=SPREADSHEET_ID, range='A:F').execute()
            existing_values = result.get('values', [])
            logging.info(f"Успешно получены данные из таблицы. Строк: {len(existing_values)}")
        except Exception as e:
            logging.error(f"Ошибка при получении данных из таблицы: {str(e)}")
            raise
        
        if not existing_values:
            headers = [['№', 'Пользователь', 'ФИО', 'Пол', 'Возраст', 'Дата проверки', 'Дата и время записи']]
            service.spreadsheets().values().update(
                spreadsheetId=SPREADSHEET_ID,
                range='A1:G1',
                valueInputOption='RAW',
                body={'values': headers}
            ).execute()
            existing_values = headers
            logging.info("Созданы заголовки для пустой таблицы")
        
        name = patient_info.get('client_name', '') or patient_info.get('name', '')
        age = str(patient_info.get('client_age', '') or patient_info.get('age', ''))
        test_time = patient_info.get('test_date', '') or patient_info.get('test_time', '')
        gender = patient_info.get('gender', '')

        name = name.strip()
        age = age.strip()
        test_time = test_time.strip().replace(os.path.join("r"), '')
        gender = gender.strip()
        
        last_number = 0
        for row in existing_values[1:]:
            if len(row) > 0 and row[0].isdigit():
                last_number = max(last_number, int(row[0]))
        
        new_row = [str(last_number + 1), username, name, gender, age, test_time, current_time]
        
        is_duplicate = False
        for row in existing_values[1:]:
            if len(row) >= 5 and row[2] == name and row[5] == test_time:  # Обновляем индексы для проверки дубликатов
                is_duplicate = True
                break
        
        if not is_duplicate:
            body = {'values': [new_row]}
            service.spreadsheets().values().append(
                spreadsheetId=SPREADSHEET_ID,
                range='A:G',  # Обновляем диапазон для включения нового столбца
                valueInputOption='RAW',
                insertDataOption='INSERT_ROWS',
                body=body
            ).execute()
            logging.info("Данные успешно добавлены в Google Sheets")
        else:
            logging.info(f"Запись для клиента {name} с временем тестирования {test_time} уже существует")
        
    except Exception as e:
        logging.error(f"Ошибка при сохранении в Google Sheets: %s", str(e))
        raise

def auto_save_report(patient_info, tree, get_element_value):
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        name = ''.join(c for c in (patient_info.get('client_name', '') or patient_info.get('name', '')) if c.isalnum() or c == ' ')
        name = name.replace(' ', '_')
        test_time = ''.join(c for c in (patient_info.get('test_date', '') or patient_info.get('test_time', '')) if c.isalnum() or c in '.:')
        test_time = test_time.replace('.', '_').replace(':', '-')
        file_name = f"{name}_{test_time}.pptx"
        output_pptx = os.path.join(script_dir, 'reports', file_name)
        
        os.makedirs(os.path.join(script_dir, 'reports'), exist_ok=True)
        save_to_pptx(output_pptx, patient_info, tree, get_element_value)
        
    except Exception as e:
        logging.error(f"Ошибка при автоматическом сохранении отчета: %s", str(e))
        raise

def save_report_function(data, patient_info, tree, get_element_value, context):
    try:
        # Нормализация информации о пациенте
        normalized_info = {
            'name': patient_info.get('name', patient_info.get('client_name', '')),
            'age': patient_info.get('age', patient_info.get('client_age', '')),
            'gender': patient_info.get('gender', ''),
            'body_type': patient_info.get('body_type', ''),
            'test_time': patient_info.get('test_time', patient_info.get('test_date', '')).replace(os.path.join("r"), '').strip()
        }
        
        # Попытка сохранить в Google Sheets
        google_sheets_success = False
        try:
            save_to_google_sheets(normalized_info, tree, context)
            google_sheets_success = True
            logging.info("Данные успешно сохранены в Google Sheets")
        except Exception as e:
            logging.error(f"Ошибка при сохранении в Google Sheets: {str(e)}")
            traceback.print_exc()

        # Сохраняем отчет локально независимо от результата Google Sheets
        auto_save_report(normalized_info, tree, get_element_value)
        
        # Логируем результаты сохранения
        log_dir = os.path.join(os.path.dirname(__file__), "logs")
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, "reports_log.txt")
        current_time = datetime.datetime.now().strftime("%d.%m.%Y %H:%M")
        
        log_entry = (
            f"Дата записи: {current_time}\n"
            f"Имя клиента: {normalized_info.get('name', '')}\n"
            f"Оператор: {context.get('user_name', context.get('username', 'Неизвестный'))}\n"
            f"Пол: {normalized_info.get('gender', '')}\n"
            f"Возраст: {normalized_info.get('age', '')}\n"
            f"Дата проверки: {normalized_info.get('test_time', '')}\n"
            f"Сохранено в Google Sheets: {'Да' if google_sheets_success else 'Нет'}\n"
            f"{'='*50}\n"
        )
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)
        logging.info(f"Лог успешно сохранен в {log_file}")
        
    except Exception as e:
        logging.error(f"Ошибка при сохранении отчета: %s", str(e))
        raise

def fix_line_breaks(text):
    # Заменяем Windows переносы на Unix
    text = text.replace('\r\n', '\n')
    
    # Исправляем уже поврежденные переносы
    text = text.replace('n/', '\n')
    text = text.replace('/n', '\n')
    
    return text
