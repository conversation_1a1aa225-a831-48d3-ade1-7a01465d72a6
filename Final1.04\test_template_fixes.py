#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Тестовый скрипт для проверки исправлений шаблона PowerPoint
"""

import re
import os
from datetime import datetime

def test_bmi_calculation():
    """Тестирует функцию расчета ИМТ"""
    def calculate_bmi(body_type_str):
        try:
            if not body_type_str:
                return "Данные не указаны"
            
            # Извлекаем рост и вес из строки
            height_match = re.search(r'(\d+(?:\.\d+)?)\s*cm', body_type_str, re.IGNORECASE)
            weight_match = re.search(r'(\d+(?:\.\d+)?)\s*kg', body_type_str, re.IGNORECASE)
            
            if not height_match or not weight_match:
                return "Некорректные данные"
            
            height_cm = float(height_match.group(1))
            weight_kg = float(weight_match.group(1))
            
            # Переводим рост в метры
            height_m = height_cm / 100
            
            # Рассчитываем ИМТ
            bmi = weight_kg / (height_m ** 2)
            
            # Определяем интерпретацию ИМТ
            if bmi < 18.5:
                interpretation = "Недостаточный вес"
            elif 18.5 <= bmi < 25:
                interpretation = "Нормальный вес"
            elif 25 <= bmi < 30:
                interpretation = "Избыточный вес"
            else:
                interpretation = "Ожирение"
            
            return f"{bmi:.1f} ({interpretation})"
            
        except (ValueError, AttributeError) as e:
            print(f"Ошибка при расчете ИМТ: {e}")
            return "Ошибка расчета"
    
    print("=== Тестирование расчета ИМТ ===")
    test_cases = [
        ("159cm, 58kg", "22.9 (Нормальный вес)"),
        ("150cm, 57kg", "25.3 (Избыточный вес)"),
        ("170cm, 70kg", "24.2 (Нормальный вес)"),
    ]
    
    all_passed = True
    for input_data, expected in test_cases:
        result = calculate_bmi(input_data)
        passed = result == expected
        all_passed = all_passed and passed
        
        status = "✅ ПРОШЕЛ" if passed else "❌ НЕ ПРОШЕЛ"
        print(f"{status}: {input_data} -> {result}")
        if not passed:
            print(f"   Ожидалось: {expected}")
    
    return all_passed

def test_date_cleaning():
    """Тестирует очистку дат"""
    def clean_date(test_date):
        if test_date:
            # Извлекаем дату и время, игнорируя лишние символы
            date_match = re.search(r'(\d{1,2}\.\d{1,2}\.\d{4})', test_date)
            time_match = re.search(r'(\d{1,2}:\d{2})', test_date)
            
            if date_match:
                clean_date = date_match.group(1)
                if time_match:
                    clean_date += f" {time_match.group(1)}"
                return clean_date
        return test_date
    
    print("\n=== Тестирование очистки дат ===")
    test_cases = [
        ("11.01.2025 x000D_ 20:12", "11.01.2025 20:12"),
        ("25.10.2024 r         09:14", "25.10.2024 09:14"),
        ("01.01.2024", "01.01.2024"),
        ("15.03.2024 14:30", "15.03.2024 14:30"),
    ]
    
    all_passed = True
    for input_data, expected in test_cases:
        result = clean_date(input_data)
        passed = result == expected
        all_passed = all_passed and passed
        
        status = "✅ ПРОШЕЛ" if passed else "❌ НЕ ПРОШЕЛ"
        print(f"{status}: '{input_data}' -> '{result}'")
        if not passed:
            print(f"   Ожидалось: '{expected}'")
    
    return all_passed

def test_template_markers():
    """Тестирует создание маркеров для шаблона"""
    print("\n=== Тестирование маркеров шаблона ===")
    
    # Тестовые данные пациента
    patient_info = {
        'name': 'Мосеева Ольга',
        'client_name': 'Мосеева Ольга',
        'age': '44',
        'client_age': '44',
        'body_type': '159cm, 58kg',
        'test_time': '11.01.2025 x000D_ 20:12',
        'test_date': '11.01.2025 x000D_ 20:12',
        'gender': 'Женский'
    }
    
    # Функция расчета ИМТ
    def calculate_bmi(body_type_str):
        try:
            if not body_type_str:
                return "Данные не указаны"
            
            height_match = re.search(r'(\d+(?:\.\d+)?)\s*cm', body_type_str, re.IGNORECASE)
            weight_match = re.search(r'(\d+(?:\.\d+)?)\s*kg', body_type_str, re.IGNORECASE)
            
            if not height_match or not weight_match:
                return "Некорректные данные"
            
            height_cm = float(height_match.group(1))
            weight_kg = float(weight_match.group(1))
            height_m = height_cm / 100
            bmi = weight_kg / (height_m ** 2)
            
            if bmi < 18.5:
                interpretation = "Недостаточный вес"
            elif 18.5 <= bmi < 25:
                interpretation = "Нормальный вес"
            elif 25 <= bmi < 30:
                interpretation = "Избыточный вес"
            else:
                interpretation = "Ожирение"
            
            return f"{bmi:.1f} ({interpretation})"
        except:
            return "Ошибка расчета"
    
    # Рассчитываем ИМТ
    bmi_value = calculate_bmi(patient_info.get('body_type', ''))
    
    # Очищаем дату
    test_date = patient_info.get('test_time', patient_info.get('test_date', ''))
    if test_date:
        date_match = re.search(r'(\d{1,2}\.\d{1,2}\.\d{4})', test_date)
        time_match = re.search(r'(\d{1,2}:\d{2})', test_date)
        
        if date_match:
            clean_date = date_match.group(1)
            if time_match:
                clean_date += f" {time_match.group(1)}"
            test_date = clean_date
    
    # Создаем маркеры (включая оба варианта BMI)
    markers = {
        '{{client_name}}': patient_info.get('name', patient_info.get('client_name', '')),
        '{{client_age}}': patient_info.get('age', patient_info.get('client_age', '')),
        '{{body_type}}': patient_info.get('body_type', ''),
        '{{test_date}}': test_date,
        '{{gender}}': patient_info.get('gender', ''),
        '{{bmi_index}}': bmi_value,
        '{{mass_index}}': bmi_value  # Альтернативный маркер для совместимости
    }
    
    print("Созданные маркеры:")
    for marker, value in markers.items():
        print(f"  {marker}: {value}")
    
    # Тестируем замену маркеров в тексте
    print("\nТестирование замены маркеров:")
    test_templates = [
        "ФИО: {{client_name}}",
        "Возраст: {{client_age}}",
        "Телосложение: {{body_type}}",
        "Дата и время: {{test_date}}",
        "ИМТ (bmi_index): {{bmi_index}}",
        "ИМТ (mass_index): {{mass_index}}"
    ]
    
    all_replaced = True
    for template in test_templates:
        result = template
        for marker, value in markers.items():
            result = result.replace(marker, str(value))
        
        has_unreplaced = '{{' in result and '}}' in result
        if has_unreplaced:
            all_replaced = False
            print(f"❌ {template} -> {result} (есть незамененные маркеры)")
        else:
            print(f"✅ {template} -> {result}")
    
    return all_replaced

def test_template_compatibility():
    """Тестирует совместимость с различными вариантами маркеров"""
    print("\n=== Тестирование совместимости маркеров ===")
    
    # Тестируем оба варианта BMI маркеров
    bmi_value = "22.9 (Нормальный вес)"
    markers = {
        '{{bmi_index}}': bmi_value,
        '{{mass_index}}': bmi_value
    }
    
    test_cases = [
        ("Индекс массы тела: {{bmi_index}}", f"Индекс массы тела: {bmi_value}"),
        ("Индекс массы тела: {{mass_index}}", f"Индекс массы тела: {bmi_value}"),
        ("ИМТ: {{bmi_index}} или {{mass_index}}", f"ИМТ: {bmi_value} или {bmi_value}")
    ]
    
    all_passed = True
    for template, expected in test_cases:
        result = template
        for marker, value in markers.items():
            result = result.replace(marker, str(value))
        
        passed = result == expected
        all_passed = all_passed and passed
        
        status = "✅ ПРОШЕЛ" if passed else "❌ НЕ ПРОШЕЛ"
        print(f"{status}: '{template}' -> '{result}'")
        if not passed:
            print(f"   Ожидалось: '{expected}'")
    
    return all_passed

if __name__ == "__main__":
    print("Запуск тестов исправлений шаблона PowerPoint\n")
    
    # Запускаем все тесты
    bmi_test = test_bmi_calculation()
    date_test = test_date_cleaning()
    markers_test = test_template_markers()
    compatibility_test = test_template_compatibility()
    
    # Общий результат
    all_tests_passed = bmi_test and date_test and markers_test and compatibility_test
    
    print(f"\n{'='*60}")
    print(f"ИТОГОВЫЙ РЕЗУЛЬТАТ: {'✅ ВСЕ ТЕСТЫ ПРОШЛИ' if all_tests_passed else '❌ ЕСТЬ ОШИБКИ'}")
    print(f"{'='*60}")
    
    if all_tests_passed:
        print("\n🎉 Все исправления шаблона работают корректно!")
        print("✅ BMI рассчитывается правильно")
        print("✅ Даты очищаются от лишних символов")
        print("✅ Маркеры создаются и заменяются корректно")
        print("✅ Поддерживается совместимость с {{bmi_index}} и {{mass_index}}")
        print("\n📋 Исправленные проблемы:")
        print("1. ❌ {{mass_index}} не заменялся -> ✅ Добавлена поддержка обоих маркеров")
        print("2. ❌ Даты с 'x000D_' символами -> ✅ Улучшена очистка дат")
        print("3. ❌ BMI не рассчитывался -> ✅ Корректный расчет и замена")
    else:
        print("\n❌ Обнаружены проблемы, требующие исправления")
