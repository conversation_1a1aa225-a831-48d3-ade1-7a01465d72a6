#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Скрипт для создания нового шаблона презентации с дополнительной первой страницей
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
import os

def create_new_template():
    """
    Создает новый шаблон презентации с дополнительной первой страницей
    """
    try:
        # Загружаем существующий шаблон
        template_path = "main_template.pptx"
        if not os.path.exists(template_path):
            print(f"Файл шаблона {template_path} не найден!")
            return False
        
        # Открываем существующий шаблон
        prs = Presentation(template_path)
        
        # Создаем новую презентацию
        new_prs = Presentation()
        
        # Добавляем новую первую страницу
        slide_layout = new_prs.slide_layouts[0]  # Используем титульный макет
        first_slide = new_prs.slides.add_slide(slide_layout)
        
        # Настраиваем заголовок
        title = first_slide.shapes.title
        title.text = "Medical Mind"
        title.text_frame.paragraphs[0].font.size = Pt(44)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
        
        # Добавляем основной контент
        content_left = 1.0
        content_top = 2.0
        content_width = 4.0
        content_height = 5.0
        
        # Левая колонка с информацией о пациенте
        left_textbox = first_slide.shapes.add_textbox(
            Inches(content_left), 
            Inches(content_top), 
            Inches(content_width), 
            Inches(content_height)
        )
        
        left_frame = left_textbox.text_frame
        left_frame.word_wrap = True
        
        # Добавляем текст с маркерами
        p1 = left_frame.paragraphs[0]
        p1.text = "ФИО"
        p1.font.size = Pt(14)
        p1.font.bold = True
        
        p2 = left_frame.add_paragraph()
        p2.text = "{{client_name}}"
        p2.font.size = Pt(12)
        
        p3 = left_frame.add_paragraph()
        p3.text = "Возраст"
        p3.font.size = Pt(14)
        p3.font.bold = True
        
        p4 = left_frame.add_paragraph()
        p4.text = "{{client_age}}"
        p4.font.size = Pt(12)
        
        p5 = left_frame.add_paragraph()
        p5.text = "Телосложение"
        p5.font.size = Pt(14)
        p5.font.bold = True
        
        p6 = left_frame.add_paragraph()
        p6.text = "{{body_type}}"
        p6.font.size = Pt(12)
        
        p7 = left_frame.add_paragraph()
        p7.text = "Дата и время"
        p7.font.size = Pt(14)
        p7.font.bold = True
        
        p8 = left_frame.add_paragraph()
        p8.text = "{{test_date}}"
        p8.font.size = Pt(12)
        
        # Правая колонка с ИМТ
        right_left = 5.5
        right_textbox = first_slide.shapes.add_textbox(
            Inches(right_left), 
            Inches(content_top), 
            Inches(content_width), 
            Inches(content_height)
        )
        
        right_frame = right_textbox.text_frame
        right_frame.word_wrap = True
        
        p9 = right_frame.paragraphs[0]
        p9.text = "Индекс массы тела"
        p9.font.size = Pt(14)
        p9.font.bold = True
        
        p10 = right_frame.add_paragraph()
        p10.text = "{{bmi_index}}"
        p10.font.size = Pt(12)
        
        # Добавляем дополнительную информацию
        p11 = right_frame.add_paragraph()
        p11.text = ""
        
        p12 = right_frame.add_paragraph()
        p12.text = "Выявленный дефицит и график коррекции:"
        p12.font.size = Pt(14)
        p12.font.bold = True
        
        # Добавляем таблицу для графика коррекции
        table_left = 1.0
        table_top = 4.5
        table_width = 8.5
        table_height = 2.0
        
        table_shape = first_slide.shapes.add_table(4, 3, 
                                                  Inches(table_left), 
                                                  Inches(table_top), 
                                                  Inches(table_width), 
                                                  Inches(table_height))
        table = table_shape.table
        
        # Заполняем заголовки таблицы
        table.cell(0, 0).text = "Утро"
        table.cell(0, 1).text = "День"
        table.cell(0, 2).text = "Вечер"
        
        # Добавляем нижний текст
        bottom_textbox = first_slide.shapes.add_textbox(
            Inches(1.0), 
            Inches(7.0), 
            Inches(8.5), 
            Inches(1.5)
        )
        
        bottom_frame = bottom_textbox.text_frame
        bottom_frame.word_wrap = True
        
        p13 = bottom_frame.paragraphs[0]
        p13.text = "Не совместимы текущим курсом:"
        p13.font.size = Pt(12)
        p13.font.bold = True
        
        p14 = bottom_frame.add_paragraph()
        p14.text = ""
        
        p15 = bottom_frame.add_paragraph()
        p15.text = "Уважаемый фармацевт!"
        p15.font.size = Pt(12)
        p15.font.bold = True
        
        p16 = bottom_frame.add_paragraph()
        p16.text = "Перед вами экспресс-результаты биомониторинга по 49 элементам. Используйте их для точного подбора БАД с учетом выявленных дефицитов, помня об опасности аллергических реакций."
        p16.font.size = Pt(10)
        
        p17 = bottom_frame.add_paragraph()
        p17.text = "Метод прост в применении, но эффективен и подтвержден клиническими исследованиями в клинике профзаболеваний ФБУН «Новосибирский НИИ гигиены» Роспотребнадзора."
        p17.font.size = Pt(10)
        
        p18 = bottom_frame.add_paragraph()
        p18.text = "Подробности — далее в отчете..."
        p18.font.size = Pt(10)
        
        # Копируем все слайды из оригинального шаблона
        for slide in prs.slides:
            # Копируем макет слайда
            slide_layout = new_prs.slide_layouts[0]  # Используем базовый макет
            new_slide = new_prs.slides.add_slide(slide_layout)
            
            # Копируем все элементы со слайда
            for shape in slide.shapes:
                # Здесь можно добавить логику копирования элементов
                # Пока оставляем простое копирование
                pass
        
        # Сохраняем новый шаблон
        new_template_path = "main_template_new.pptx"
        new_prs.save(new_template_path)
        
        print(f"Новый шаблон создан: {new_template_path}")
        return True
        
    except Exception as e:
        print(f"Ошибка при создании нового шаблона: {e}")
        return False

if __name__ == "__main__":
    create_new_template()
