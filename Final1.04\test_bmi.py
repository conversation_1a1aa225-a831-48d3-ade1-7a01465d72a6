#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Тестовый скрипт для проверки функциональности расчета ИМТ
"""

import re
import logging

def calculate_bmi(body_type_str):
    """
    Рассчитывает ИМТ из строки телосложения в формате "150cm, 57kg"
    Возвращает строку с ИМТ и интерпретацией
    """
    try:
        if not body_type_str:
            return "Данные не указаны"
        
        # Извлекаем рост и вес из строки
        height_match = re.search(r'(\d+(?:\.\d+)?)\s*cm', body_type_str, re.IGNORECASE)
        weight_match = re.search(r'(\d+(?:\.\d+)?)\s*kg', body_type_str, re.IGNORECASE)
        
        if not height_match or not weight_match:
            return "Некорректные данные"
        
        height_cm = float(height_match.group(1))
        weight_kg = float(weight_match.group(1))
        
        # Переводим рост в метры
        height_m = height_cm / 100
        
        # Рассчитываем ИМТ
        bmi = weight_kg / (height_m ** 2)
        
        # Определяем интерпретацию ИМТ
        if bmi < 18.5:
            interpretation = "Недостаточный вес"
        elif 18.5 <= bmi < 25:
            interpretation = "Нормальный вес"
        elif 25 <= bmi < 30:
            interpretation = "Избыточный вес"
        else:
            interpretation = "Ожирение"
        
        return f"{bmi:.1f} ({interpretation})"
        
    except (ValueError, AttributeError) as e:
        print(f"Ошибка при расчете ИМТ: {e}")
        return "Ошибка расчета"

def test_bmi_calculation():
    """
    Тестирует функцию расчета ИМТ с различными входными данными
    """
    test_cases = [
        ("150cm, 57kg", "25.3 (Избыточный вес)"),
        ("170cm, 70kg", "24.2 (Нормальный вес)"),
        ("180cm, 90kg", "27.8 (Избыточный вес)"),
        ("160cm, 45kg", "17.6 (Недостаточный вес)"),
        ("175cm, 100kg", "32.7 (Ожирение)"),
        ("", "Данные не указаны"),
        ("некорректные данные", "Некорректные данные"),
        ("170 см, 70 кг", "Некорректные данные"),  # Кириллица
        ("170.5cm, 72.3kg", "24.9 (Нормальный вес)"),  # Десятичные числа
    ]
    
    print("Тестирование функции расчета ИМТ:")
    print("=" * 50)
    
    for i, (input_data, expected) in enumerate(test_cases, 1):
        result = calculate_bmi(input_data)
        status = "✓" if result == expected else "✗"
        
        print(f"Тест {i}: {status}")
        print(f"  Входные данные: '{input_data}'")
        print(f"  Ожидаемый результат: {expected}")
        print(f"  Фактический результат: {result}")
        
        if result != expected:
            print(f"  ОШИБКА: Результаты не совпадают!")
        print()

def test_pptx_markers():
    """
    Тестирует создание маркеров для PPTX
    """
    print("Тестирование создания маркеров для PPTX:")
    print("=" * 50)
    
    # Тестовые данные пациента
    patient_info = {
        'name': 'Иванов Иван Иванович',
        'client_name': 'Иванов Иван Иванович',
        'age': '35',
        'client_age': '35',
        'body_type': '175cm, 80kg',
        'test_time': '2024-01-15 14:30:00',
        'test_date': '2024-01-15 14:30:00',
        'gender': 'Мужской'
    }
    
    # Рассчитываем ИМТ
    bmi_value = calculate_bmi(patient_info.get('body_type', ''))
    
    # Создаем маркеры
    markers = {
        '{{client_name}}': patient_info.get('name', patient_info.get('client_name', '')),
        '{{client_age}}': patient_info.get('age', patient_info.get('client_age', '')),
        '{{body_type}}': patient_info.get('body_type', ''),
        '{{test_date}}': patient_info.get('test_time', patient_info.get('test_date', '')),
        '{{gender}}': patient_info.get('gender', ''),
        '{{bmi_index}}': bmi_value
    }
    
    print("Созданные маркеры:")
    for marker, value in markers.items():
        print(f"  {marker}: {value}")
    
    print("\nТестирование замены маркеров в тексте:")
    test_text = "Пациент {{client_name}}, возраст {{client_age}} лет, ИМТ: {{bmi_index}}"
    
    result_text = test_text
    for marker, value in markers.items():
        result_text = result_text.replace(marker, str(value))
    
    print(f"  Исходный текст: {test_text}")
    print(f"  Результат: {result_text}")

if __name__ == "__main__":
    test_bmi_calculation()
    print("\n" + "=" * 70 + "\n")
    test_pptx_markers()
