#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Тестовый скрипт для создания PowerPoint презентации с исправленными маркерами
"""

import os
import sys
from datetime import datetime

def test_pptx_generation():
    """Тестирует создание PowerPoint презентации с исправленными маркерами"""
    try:
        # Импортируем функцию сохранения отчета
        from save_report import save_to_pptx
        
        print("🔧 Тестирование создания PowerPoint с исправленными маркерами")
        print("=" * 70)
        
        # Тестовые данные пациента (те же, что показаны на изображении)
        patient_info = {
            'name': 'Мосеева Ольга',
            'client_name': 'Мосеева Ольга',
            'age': '44',
            'client_age': '44',
            'body_type': '159cm, 58kg',
            'test_time': '11.01.2025 x000D_ 20:12',  # Проблемная дата с лишними символами
            'test_date': '11.01.2025 x000D_ 20:12',
            'gender': 'Женский'
        }
        
        print("📋 Данные пациента:")
        for key, value in patient_info.items():
            print(f"  {key}: {value}")
        
        # Создаем минимальный mock для tree (TreeView)
        class MockTree:
            def get_children(self):
                return ['item1', 'item2', 'item3']
            
            def item(self, item_id):
                # Возвращаем тестовые данные элементов
                test_data = {
                    'item1': {'values': ['Ca', '1200', 'Норма', 'Кальций']},
                    'item2': {'values': ['Fe', '15', 'Дефицит', 'Железо']},
                    'item3': {'values': ['Zn', '12', 'Норма', 'Цинк']}
                }
                return test_data.get(item_id, {'values': ['', '', '', '']})
        
        def mock_get_element_value(element):
            """Mock функция для получения значений элементов"""
            element_values = {
                'Ca': 1200,
                'Fe': 15,
                'Zn': 12
            }
            return element_values.get(element, 0)
        
        # Создаем mock объекты
        mock_tree = MockTree()
        
        # Путь для тестового файла
        test_file_path = os.path.join(os.path.dirname(__file__), 'test_fixed_template.pptx')
        
        print(f"\n📁 Создание файла: {test_file_path}")
        
        # Вызываем функцию сохранения
        result = save_to_pptx(
            file_path=test_file_path,
            patient_info=patient_info,
            tree=mock_tree,
            get_element_value=mock_get_element_value
        )
        
        if result:
            print("✅ Функция save_to_pptx выполнена успешно")
            
            if os.path.exists(test_file_path):
                file_size = os.path.getsize(test_file_path)
                print(f"✅ Файл создан успешно")
                print(f"📊 Размер файла: {file_size:,} байт")
                
                # Проверяем, что файл не пустой
                if file_size > 10000:  # Минимальный размер для PowerPoint файла
                    print("✅ Размер файла корректный")
                    
                    print(f"\n🎯 Ожидаемые исправления в презентации:")
                    print("1. ✅ {{mass_index}} должен быть заменен на: 22.9 (Нормальный вес)")
                    print("2. ✅ {{test_date}} должен показывать: 11.01.2025 20:12 (без x000D_)")
                    print("3. ✅ {{client_name}} должен показывать: Мосеева Ольга")
                    print("4. ✅ {{client_age}} должен показывать: 44")
                    print("5. ✅ {{body_type}} должен показывать: 159cm, 58kg")
                    
                    print(f"\n📂 Откройте файл для проверки: {test_file_path}")
                    print("🔍 Убедитесь, что:")
                    print("   - Нет незамененных маркеров вида {{marker_name}}")
                    print("   - BMI отображается корректно")
                    print("   - Дата не содержит лишних символов")
                    print("   - Все данные пациента заполнены правильно")
                    
                    return True
                else:
                    print("❌ Файл слишком маленький, возможно поврежден")
                    return False
            else:
                print("❌ Файл не был создан")
                return False
        else:
            print("❌ Функция save_to_pptx вернула False")
            return False
            
    except ImportError as e:
        print(f"❌ Ошибка импорта: {e}")
        print("Убедитесь, что файл save_report.py доступен")
        return False
    except Exception as e:
        print(f"❌ Ошибка при создании презентации: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_marker_replacement_logic():
    """Тестирует логику замены маркеров отдельно"""
    print("\n🔧 Тестирование логики замены маркеров")
    print("=" * 50)
    
    # Имитируем процесс создания маркеров из save_report.py
    patient_info = {
        'name': 'Мосеева Ольга',
        'age': '44',
        'body_type': '159cm, 58kg',
        'test_time': '11.01.2025 x000D_ 20:12',
        'gender': 'Женский'
    }
    
    # Функция расчета BMI (копия из save_report.py)
    def calculate_bmi(body_type_str):
        try:
            if not body_type_str:
                return "Данные не указаны"
            
            import re
            height_match = re.search(r'(\d+(?:\.\d+)?)\s*cm', body_type_str, re.IGNORECASE)
            weight_match = re.search(r'(\d+(?:\.\d+)?)\s*kg', body_type_str, re.IGNORECASE)
            
            if not height_match or not weight_match:
                return "Некорректные данные"
            
            height_cm = float(height_match.group(1))
            weight_kg = float(weight_match.group(1))
            height_m = height_cm / 100
            bmi = weight_kg / (height_m ** 2)
            
            if bmi < 18.5:
                interpretation = "Недостаточный вес"
            elif 18.5 <= bmi < 25:
                interpretation = "Нормальный вес"
            elif 25 <= bmi < 30:
                interpretation = "Избыточный вес"
            else:
                interpretation = "Ожирение"
            
            return f"{bmi:.1f} ({interpretation})"
        except:
            return "Ошибка расчета"
    
    # Рассчитываем BMI
    bmi_value = calculate_bmi(patient_info.get('body_type', ''))
    print(f"📊 Рассчитанный BMI: {bmi_value}")
    
    # Очищаем дату (логика из исправленного кода)
    test_date = patient_info.get('test_time', '')
    if test_date:
        import re
        date_match = re.search(r'(\d{1,2}\.\d{1,2}\.\d{4})', test_date)
        time_match = re.search(r'(\d{1,2}:\d{2})', test_date)
        
        if date_match:
            clean_date = date_match.group(1)
            if time_match:
                clean_date += f" {time_match.group(1)}"
            test_date = clean_date
    
    print(f"📅 Очищенная дата: {test_date}")
    
    # Создаем маркеры (включая оба варианта BMI)
    markers = {
        '{{client_name}}': patient_info.get('name', ''),
        '{{client_age}}': patient_info.get('age', ''),
        '{{body_type}}': patient_info.get('body_type', ''),
        '{{test_date}}': test_date,
        '{{gender}}': patient_info.get('gender', ''),
        '{{bmi_index}}': bmi_value,
        '{{mass_index}}': bmi_value  # Исправление: добавлен альтернативный маркер
    }
    
    print("\n📋 Созданные маркеры:")
    for marker, value in markers.items():
        print(f"  {marker}: {value}")
    
    # Тестируем замену в примерах текста из шаблона
    test_texts = [
        "ФИО: {{client_name}}",
        "Возраст: {{client_age}}",
        "Телосложение: {{body_type}}",
        "Дата и время: {{test_date}}",
        "Индекс массы тела: {{mass_index}}",  # Проблемный маркер из изображения
        "BMI: {{bmi_index}}"
    ]
    
    print("\n🔄 Тестирование замены маркеров:")
    all_replaced = True
    for text in test_texts:
        original = text
        for marker, value in markers.items():
            text = text.replace(marker, str(value))
        
        has_unreplaced = '{{' in text and '}}' in text
        status = "❌ ЕСТЬ НЕЗАМЕНЕННЫЕ" if has_unreplaced else "✅ ВСЕ ЗАМЕНЕНО"
        print(f"  {status}: {original} -> {text}")
        
        if has_unreplaced:
            all_replaced = False
    
    return all_replaced

if __name__ == "__main__":
    print("🚀 Запуск тестирования исправлений PowerPoint шаблона\n")
    
    # Тестируем логику замены маркеров
    logic_test = test_marker_replacement_logic()
    
    # Тестируем создание презентации
    pptx_test = test_pptx_generation()
    
    print(f"\n{'='*70}")
    if logic_test and pptx_test:
        print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ УСПЕШНО!")
        print("✅ Логика замены маркеров работает корректно")
        print("✅ PowerPoint презентация создается без ошибок")
        print("\n📝 Исправленные проблемы:")
        print("1. {{mass_index}} теперь заменяется на корректное значение BMI")
        print("2. Даты очищаются от символов 'x000D_' и других артефактов")
        print("3. Все маркеры пациента заполняются правильными данными")
    else:
        print("❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ")
        if not logic_test:
            print("❌ Проблемы с логикой замены маркеров")
        if not pptx_test:
            print("❌ Проблемы с созданием PowerPoint презентации")
    print(f"{'='*70}")
