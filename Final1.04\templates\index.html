{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="text-center mb-4">
        <h1 class="mt-3">Интеллектуальная обработка данных биомониторинга</h1>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body p-4">
            <div class="text-center">
                <div class="my-4">
                    <img src="{{ url_for('static', filename='images/logo-split.svg') }}" width="60" class="mb-3">
                    <h3>Перетащите файл отчета сюда</h3>
                    <p class="text-muted">или</p>
                    <div>
                        <button id="fileSelectBtn" class="btn btn-primary">
                            Выбрать файл
                        </button>
                    </div>
                </div>
            </div>
            <form id="uploadForm" enctype="multipart/form-data" class="d-none">
                <input type="file" name="file" id="fileInput" accept=".htm,.html,.xlsx,.xls">
            </form>
        </div>
    </div>
    
    <div id="loadingContainer" class="text-center mb-4" style="display: none;">
        <div class="spinner-border text-primary mb-2" role="status">
            <span class="visually-hidden">Загрузка...</span>
        </div>
        <p id="loadingText">Обработка файла...</p>
    </div>
    
    <div id="patientInfo" class="card shadow-sm mb-4" style="display: none;">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Информация о клиенте</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p id="nameRow" class="patient-info-row mb-3"></p>
                    <p id="genderRow" class="patient-info-row mb-3"></p>
                    <p id="ageRow" class="patient-info-row mb-3"></p>
                </div>
                <div class="col-md-6">
                    <p id="bodyTypeRow" class="patient-info-row mb-3"></p>
                    <p id="bmiRow" class="patient-info-row mb-3"></p>
                    <p id="testTimeRow" class="patient-info-row mb-3"></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Таблица результатов анализа -->
    <div id="resultsTable" class="card shadow-sm mb-4" style="display: none;">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-table me-2"></i>Результаты анализа</h5>
            <div>
                <select id="filterResults" class="form-select form-select-sm">
                    <option value="all">Все элементы</option>
                    <option value="problems">Только отклонения</option>
                    <option value="major">Значительные отклонения (±2, ±3)</option>
                </select>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                <table class="table table-hover mb-0">
                    <thead class="bg-light sticky-top">
                        <tr>
                            <th>Элемент</th>
                            <th>Результат</th>
                            <th>Индекс</th>
                            <th>Статус</th>
                            <th>Описание</th>
                        </tr>
                    </thead>
                    <tbody id="resultsTableBody">
                        <!-- Данные будут заполнены через JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div id="summaryContainer" style="display: none;">
        <div class="mb-4">
            <button id="saveReportBtn" class="btn btn-primary d-block w-100 py-3">
                <i class="bi bi-save me-2"></i> Сохранить отчет
            </button>
        </div>
        <div id="saveResultMessage"></div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-speedometer2"></i> Информационная панель</h5>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-primary active" id="stats-today">Сегодня</button>
                        <button class="btn btn-sm btn-outline-primary" id="stats-week">Неделя</button>
                        <button class="btn btn-sm btn-outline-primary" id="stats-month">Месяц</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Статистика отчетов -->
                        <div class="col-md-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-muted mb-2">Созданные отчеты</h6>
                                    <h2 class="mb-1" id="reports-count">{{ todays_reports }}</h2>
                                    <span class="badge bg-success"><i class="bi bi-arrow-up"></i> 
                                        {% if total_reports > 0 %}
                                            {{ ((todays_reports / total_reports) * 100)|round|int }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Клиенты -->
                        <div class="col-md-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-muted mb-2">Обслужено клиентов</h6>
                                    <h2 class="mb-1" id="clients-count">{{ clients_today }}</h2>
                                    <span class="badge bg-success"><i class="bi bi-arrow-up"></i> 
                                        {% if total_clients > 0 %}
                                            {{ ((clients_today / total_clients) * 100)|round|int }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Среднее время -->
                        <div class="col-md-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-muted mb-2">Среднее время</h6>
                                    <h2 class="mb-1">{{ avg_time }}</h2>
                                    <span class="badge bg-info"><i class="bi bi-clock"></i> мин</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Успешность отчетов -->
                        <div class="col-md-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-muted mb-2">Успешность</h6>
                                    <h2 class="mb-1">{{ success_rate }}%</h2>
                                    <span class="badge bg-success"><i class="bi bi-check-circle"></i> Отчетов</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- График активности -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="mb-3">Активность по часам</h6>
                            <div class="chart-container" style="position: relative; height:200px;">
                                <div class="d-flex justify-content-between mb-2">
                                    <div class="small text-muted">00:00</div>
                                    <div class="small text-muted">06:00</div>
                                    <div class="small text-muted">12:00</div>
                                    <div class="small text-muted">18:00</div>
                                    <div class="small text-muted">24:00</div>
                                </div>
                                <div class="progress" style="height: 25px;">
                                    {% for hour in range(24) %}
                                        {% set reports_width = hourly_stats[hour]['reports'] * 5 if hourly_stats[hour]['reports'] > 0 else 1 %}
                                        {% set clients_width = hourly_stats[hour]['clients'] * 5 if hourly_stats[hour]['clients'] > 0 else 1 %}
                                        
                                        <div class="progress-bar bg-primary" role="progressbar" style="width: {{ reports_width }}%" 
                                             title="Отчетов: {{ hourly_stats[hour]['reports'] }}"></div>
                                        <div class="progress-bar bg-info" role="progressbar" style="width: {{ clients_width }}%" 
                                             title="Клиентов: {{ hourly_stats[hour]['clients'] }}"></div>
                                    {% endfor %}
                                </div>
                                <div class="d-flex justify-content-between mt-2">
                                    <div class="small text-muted"><i class="bi bi-circle-fill text-primary"></i> Отчеты</div>
                                    <div class="small text-muted"><i class="bi bi-circle-fill text-info"></i> Клиенты</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Напоминания и уведомления -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0"><i class="bi bi-bell"></i> Напоминания</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% if reminders %}
                            {% for reminder in reminders %}
                                <a href="{{ url_for('admin_client_details', client_id=reminder.id) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <p class="mb-1 fw-bold">{{ reminder.name }}</p>
                                        <small class="text-muted">Повторное тестирование через {{ reminder.days }} {{ 'день' if reminder.days == 1 else 'дня' if 1 < reminder.days < 5 else 'дней' }}</small>
                                    </div>
                                    <span class="badge {{ reminder.status_class }} rounded-pill">{{ reminder.days }} {{ 'день' if reminder.days == 1 else 'дня' if 1 < reminder.days < 5 else 'дней' }}</span>
                                </a>
                            {% endfor %}
                        {% else %}
                            <div class="list-group-item text-center py-3">
                                <p class="text-muted mb-0">Нет запланированных тестирований</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="card-footer bg-white text-center">
                    <a href="{{ url_for('admin_clients') }}" class="btn btn-sm btn-outline-primary">Все напоминания</a>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0"><i class="bi bi-exclamation-circle"></i> Уведомления</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% if alerts %}
                            {% for alert in alerts %}
                                <a href="{{ url_for('view_report', report_id=alert.id) }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">{{ alert.title }}</h6>
                                        <small class="text-muted">{{ alert.time_text }}</small>
                                    </div>
                                    <p class="mb-1">{{ alert.description }}</p>
                                </a>
                            {% endfor %}
                        {% else %}
                            <div class="list-group-item text-center py-3">
                                <p class="text-muted mb-0">Нет новых уведомлений</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="card-footer bg-white text-center">
                    <a href="{{ url_for('admin_reports') }}" class="btn btn-sm btn-outline-primary">Все уведомления</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Календарь встреч -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-calendar-event"></i> Календарь встреч</h5>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-primary active">День</button>
                        <button class="btn btn-sm btn-outline-primary">Неделя</button>
                        <button class="btn btn-sm btn-outline-primary">Месяц</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Сегодня: {{ today_date }}</h6>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-chevron-left"></i></button>
                            <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-chevron-right"></i></button>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="10%">Время</th>
                                    <th width="25%">Клиент</th>
                                    <th width="25%">Тип</th>
                                    <th width="25%">Статус</th>
                                    <th width="15%">Действия</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if calendar_events %}
                                    {% for event in calendar_events %}
                                        <tr>
                                            <td>{{ event.time }}</td>
                                            <td>{{ event.client }}</td>
                                            <td>{{ event.type }}</td>
                                            <td><span class="badge {{ event.status_class }}">{{ event.status }}</span></td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                                    <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-pencil"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="5" class="text-center py-3">Нет запланированных встреч на сегодня</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="text-center mt-3">
                        <button class="btn btn-primary"><i class="bi bi-plus-circle"></i> Добавить встречу</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const dropArea = document.querySelector('.card-body');
        const fileInput = document.getElementById('fileInput');
        const uploadForm = document.getElementById('uploadForm');
        const fileSelectBtn = document.getElementById('fileSelectBtn');
        const loading = document.getElementById('loadingContainer');
        const summaryContainer = document.getElementById('summaryContainer');
        
        // Объявляем переменные для хранения данных пациента и обработанных данных
        let patientInfo = {};
        let parsedData = [];
        
        // Инициализация переключателей статистики
        const statsToday = document.getElementById('stats-today');
        const statsWeek = document.getElementById('stats-week');
        const statsMonth = document.getElementById('stats-month');
        const reportsCount = document.getElementById('reports-count');
        const clientsCount = document.getElementById('clients-count');
        
        // Сохраняем значения для разных периодов
        const data = {
            reports: {
                today: {{ todays_reports }},
                week: {{ weekly_reports }},
                month: {{ monthly_reports }}
            },
            clients: {
                today: {{ clients_today }},
                week: {{ clients_week }},
                month: {{ clients_month }}
            }
        };
        
        // Функция обновления статистики
        function updateStats(period) {
            reportsCount.textContent = data.reports[period];
            clientsCount.textContent = data.clients[period];
            
            // Обновление активной кнопки
            [statsToday, statsWeek, statsMonth].forEach(btn => btn.classList.remove('active'));
            
            if (period === 'today') statsToday.classList.add('active');
            else if (period === 'week') statsWeek.classList.add('active');
            else if (period === 'month') statsMonth.classList.add('active');
        }
        
        // Обработчики кликов по кнопкам периода
        statsToday.addEventListener('click', () => updateStats('today'));
        statsWeek.addEventListener('click', () => updateStats('week'));
        statsMonth.addEventListener('click', () => updateStats('month'));
        
        // Обработчики событий для drag & drop
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });
        
        function highlight() {
            dropArea.classList.add('bg-light');
        }
        
        function unhighlight() {
            dropArea.classList.remove('bg-light');
        }
        
        dropArea.addEventListener('drop', handleDrop, false);
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            
            if (files.length > 0) {
                fileInput.files = files;
                handleFiles(files);
            }
        }
        
        fileSelectBtn.addEventListener('click', function() {
            fileInput.click();
        });
        
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                handleFiles(this.files);
            }
        });
        
        function handleFiles(files) {
            const formData = new FormData(uploadForm);
            
            // Показываем индикатор загрузки
            loading.style.display = 'block';
            
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Отобразить информацию о пациенте
                    if (data.patient_info) {
                        displayPatientInfo(data.patient_info);
                        // Сохраняем данные пациента
                        patientInfo = data.patient_info;
                    }
                    
                    // Сохраняем обработанные данные
                    if (data.data) {
                        parsedData = data.data;
                        console.log('Данные сохранены:', parsedData.length, 'элементов');
                        console.log('Пример данных элемента:', parsedData[0]);
                        // Отображаем таблицу результатов
                        displayResultsTable(parsedData);
                    } else if (data.parsed_data) {
                        parsedData = data.parsed_data;
                        console.log('Данные сохранены из parsed_data:', parsedData.length, 'элементов');
                        console.log('Пример данных элемента:', parsedData[0]);
                        // Отображаем таблицу результатов
                        displayResultsTable(parsedData);
                    } else {
                        console.warn('Не найдены данные ни в data, ни в parsed_data');
                    }
                    
                    // Показать кнопку сохранения отчета
                    summaryContainer.style.display = 'block';
                } else {
                    alert('Ошибка обработки файла: ' + (data.error || 'Неизвестная ошибка'));
                }
            })
            .catch(error => {
                console.error('Ошибка:', error);
                alert('Произошла ошибка при загрузке файла');
            })
            .finally(() => {
                loading.style.display = 'none';
            });
        }

        // Функция для расчета ИМТ
        function calculateBMI(bodyTypeStr) {
            try {
                if (!bodyTypeStr) {
                    return null;
                }

                // Извлекаем рост и вес из строки
                const heightMatch = bodyTypeStr.match(/(\d+(?:\.\d+)?)\s*cm/i);
                const weightMatch = bodyTypeStr.match(/(\d+(?:\.\d+)?)\s*kg/i);

                if (!heightMatch || !weightMatch) {
                    return null;
                }

                const heightCm = parseFloat(heightMatch[1]);
                const weightKg = parseFloat(weightMatch[1]);

                if (heightCm <= 0 || weightKg <= 0) {
                    return null;
                }

                // Переводим рост в метры
                const heightM = heightCm / 100;

                // Рассчитываем ИМТ
                const bmi = weightKg / (heightM * heightM);

                // Определяем интерпретацию ИМТ
                let interpretation;
                if (bmi < 18.5) {
                    interpretation = "Недостаточный вес";
                } else if (bmi < 25) {
                    interpretation = "Нормальный вес";
                } else if (bmi < 30) {
                    interpretation = "Избыточный вес";
                } else {
                    interpretation = "Ожирение";
                }

                return `${bmi.toFixed(1)} (${interpretation})`;

            } catch (error) {
                console.error('Ошибка при расчете ИМТ:', error);
                return null;
            }
        }

        // Обработчик кнопки сохранения отчета
        document.getElementById('saveReportBtn').addEventListener('click', async function() {
            const loading = document.getElementById('loadingContainer');
            loading.style.display = 'block';
            document.getElementById('loadingText').textContent = 'Создание отчета...';
            
            // Отладочный вывод: проверяем данные перед отправкой
            console.log('Данные пациента перед отправкой:', patientInfo);
            console.log('Количество обработанных данных перед отправкой:', parsedData.length);
            console.log('Первые 3 элемента данных:', parsedData.slice(0, 3));
            
            try {
                const response = await fetch('/save-report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        patient_info: patientInfo,
                        parsed_data: parsedData
                    })
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Ошибка сервера: ${response.status}. Детали: ${errorText}`);
                }
                
                const result = await response.json();
                
                if (result.success) {
                    let message = 'Отчет успешно сохранен';
                    if (result.report_file) {
                        message += `. <a href="/download-report/${result.report_file}" download>Скачать отчет</a>`;
                        
                        // Если в результате есть тип отчета, выводим соответствующую информацию
                        if (result.report_type) {
                            if (result.report_type === 'pptx') {
                                message = 'Презентация PPTX успешно создана. ' + message;
                            } else if (result.report_type === 'pdf') {
                                message = 'PDF отчет успешно создан. ' + message;
                            }
                        }
                    }
                    
                    document.getElementById('saveResultMessage').innerHTML = `
                        <div class="alert alert-success mb-4 shadow-sm" style="background-color: #e8f5e9; border-color: #4caf50; border-left: 5px solid #4caf50; padding: 15px 20px;">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-check-circle-fill me-3" style="font-size: 1.5rem; color: #4caf50;"></i>
                                <div>
                                    <p class="mb-0" style="font-size: 1.1rem; color: #1b5e20;">${message}</p>
                                </div>
                            </div>
                        </div>`;
                    
                    // После успешного создания отчета обновляем статистику без перезагрузки страницы
                    data.reports.today += 1;
                    data.reports.week += 1;
                    data.reports.month += 1;
                    
                    // Определяем, какой период статистики сейчас активен
                    let currentPeriod = 'today';
                    if (statsWeek.classList.contains('active')) currentPeriod = 'week';
                    else if (statsMonth.classList.contains('active')) currentPeriod = 'month';
                    
                    // Обновляем отображение статистики
                    updateStats(currentPeriod);
                    
                    // Добавляем автоматическое скачивание файла
                    if (result.report_file) {
                        console.log('Начинаем скачивание файла:', result.report_file);
                        const downloadUrl = `/download-report/${result.report_file}`;
                        
                        // Создаем и настраиваем ссылку для скачивания
                        const link = document.createElement('a');
                        link.href = downloadUrl;
                        link.download = result.report_file;
                        link.style.display = 'none';
                        
                        // Добавляем ссылку в DOM и имитируем клик
                        document.body.appendChild(link);
                        setTimeout(() => {
                            link.click();
                            document.body.removeChild(link);
                            console.log('Скачивание инициировано');
                        }, 1000);
                    } else {
                        console.warn('Имя файла отчета не получено в ответе');
                    }
                } else {
                    document.getElementById('saveResultMessage').innerHTML = 
                        `<div class="alert alert-danger">${result.error || 'Ошибка при сохранении отчета'}</div>`;
                }
            } catch (error) {
                document.getElementById('saveResultMessage').innerHTML = 
                    `<div class="alert alert-danger">Ошибка при сохранении отчета</div>`;
                console.error(error);
            } finally {
                loading.style.display = 'none';
                document.getElementById('loadingText').textContent = 'Обработка файла...';
                
                // Сбрасываем состояние кнопки сохранения
                const saveButton = document.getElementById('saveReportBtn');
                saveButton.disabled = false;
                
                // Удаляем спиннер, если он есть
                const spinner = saveButton.querySelector('.spinner-border');
                if (spinner) {
                    saveButton.removeChild(spinner);
                }
            }
        });

        function displayPatientInfo(info) {
            const patientInfo = document.getElementById('patientInfo');
            patientInfo.style.display = 'block';

            document.getElementById('nameRow').innerHTML = `
                <div class="d-flex align-items-center">
                    <span class="bg-light p-2 rounded-circle me-2">
                        <i class="bi bi-person text-primary"></i>
                    </span>
                    <span>
                        <strong class="text-dark">ФИО:</strong>
                        <span class="ms-2 fs-5">${info.name || ''}</span>
                    </span>
                </div>`;
                
            document.getElementById('genderRow').innerHTML = `
                <div class="d-flex align-items-center">
                    <span class="bg-light p-2 rounded-circle me-2">
                        <i class="bi bi-gender-ambiguous text-primary"></i>
                    </span>
                    <span>
                        <strong class="text-dark">Пол:</strong>
                        <span class="ms-2 fs-5">${info.gender || ''}</span>
                    </span>
                </div>`;
                
            document.getElementById('ageRow').innerHTML = `
                <div class="d-flex align-items-center">
                    <span class="bg-light p-2 rounded-circle me-2">
                        <i class="bi bi-calendar text-primary"></i>
                    </span>
                    <span>
                        <strong class="text-dark">Возраст:</strong>
                        <span class="ms-2 fs-5">${info.age || ''}</span>
                    </span>
                </div>`;
                
            document.getElementById('bodyTypeRow').innerHTML = `
                <div class="d-flex align-items-center">
                    <span class="bg-light p-2 rounded-circle me-2">
                        <i class="bi bi-rulers text-primary"></i>
                    </span>
                    <span>
                        <strong class="text-dark">Телосложение:</strong>
                        <span class="ms-2 fs-5">${info.body_type || ''}</span>
                    </span>
                </div>`;

            // Рассчитываем и отображаем ИМТ
            let bmiText = 'Не указан';
            if (info.body_type) {
                const bmi = calculateBMI(info.body_type);
                if (bmi) {
                    bmiText = bmi;
                }
            }

            document.getElementById('bmiRow').innerHTML = `
                <div class="d-flex align-items-center">
                    <span class="bg-light p-2 rounded-circle me-2">
                        <i class="bi bi-calculator text-primary"></i>
                    </span>
                    <span>
                        <strong class="text-dark">Индекс массы тела:</strong>
                        <span class="ms-2 fs-5">${bmiText}</span>
                    </span>
                </div>`;
                
            // Очищаем дату от лишних символов
            let cleanTestTime = info.test_time || '';
            if (cleanTestTime) {
                // Извлекаем дату и время, игнорируя лишние символы
                const dateMatch = cleanTestTime.match(/(\d{1,2}\.\d{1,2}\.\d{4})/);
                const timeMatch = cleanTestTime.match(/(\d{1,2}:\d{2})/);

                if (dateMatch) {
                    cleanTestTime = dateMatch[1];
                    if (timeMatch) {
                        cleanTestTime += ` ${timeMatch[1]}`;
                    }
                }
            }

            document.getElementById('testTimeRow').innerHTML = `
                <div class="d-flex align-items-center">
                    <span class="bg-light p-2 rounded-circle me-2">
                        <i class="bi bi-clock text-primary"></i>
                    </span>
                    <span>
                        <strong class="text-dark">Дата и время:</strong>
                        <span class="ms-2 fs-5">${cleanTestTime}</span>
                    </span>
                </div>`;
        }

        // Функция для отображения таблицы результатов
        function displayResultsTable(data) {
            const resultsTable = document.getElementById('resultsTable');
            const resultsTableBody = document.getElementById('resultsTableBody');
            const filterSelect = document.getElementById('filterResults');
            
            // Переменная для хранения всех данных
            const allData = [...data];
            
            // Функция фильтрации и отображения данных
            function filterAndDisplayData() {
                // Очищаем таблицу
                resultsTableBody.innerHTML = '';
                
                // Получаем выбранный фильтр
                const filterValue = filterSelect.value;
                
                // Фильтруем данные
                let filteredData = [...allData];
                
                if (filterValue === 'problems') {
                    filteredData = allData.filter(item => {
                        const scale = parseInt(item.scale || '0');
                        return scale !== 0;
                    });
                } else if (filterValue === 'major') {
                    filteredData = allData.filter(item => {
                        const scale = parseInt(item.scale || '0');
                        return Math.abs(scale) >= 2;
                    });
                }
                
                // Отображаем данные, сортируя их по индексу (от самых проблемных к норме)
                filteredData.sort((a, b) => {
                    // Преобразуем строку в число или используем 0 как значение по умолчанию
                    const scaleA = parseInt(a.scale || '0');
                    const scaleB = parseInt(b.scale || '0');
                    
                    // Сортируем по абсолютному значению индекса (сначала отклонения)
                    return Math.abs(scaleB) - Math.abs(scaleA);
                }).forEach(item => {
                    // Только если у элемента есть имя
                    if (!item.element) return;
                    
                    // Создаем строку таблицы
                    const row = document.createElement('tr');
                    
                    // Определяем класс для индекса
                    let indexClass = 'bg-success text-white';
                    let statusText = 'Норма';
                    
                    // Определяем индекс и статус
                    const scale = parseInt(item.scale || '0');
                    
                    if (scale === -3) {
                        indexClass = 'bg-danger text-white';
                        statusText = 'Сильный дефицит';
                    } else if (scale === -2) {
                        indexClass = 'bg-warning';
                        statusText = 'Дефицит';
                    } else if (scale === -1) {
                        indexClass = 'bg-info text-white';
                        statusText = 'Легкий дефицит';
                    } else if (scale === 1) {
                        indexClass = 'bg-info text-white';
                        statusText = 'Легкий избыток';
                    } else if (scale === 2) {
                        indexClass = 'bg-warning';
                        statusText = 'Избыток';
                    } else if (scale === 3) {
                        indexClass = 'bg-danger text-white';
                        statusText = 'Сильный избыток';
                    }
                    
                    // Заполняем данные ячеек
                    row.innerHTML = `
                        <td>${item.element}</td>
                        <td>${item.result || '-'}</td>
                        <td><span class="badge ${indexClass}">${item.scale || '0'}</span></td>
                        <td>${statusText}</td>
                        <td>${item.description || 'Нет описания'}</td>
                    `;
                    
                    // Добавляем строку в таблицу
                    resultsTableBody.appendChild(row);
                });
                
                // Если нет данных
                if (resultsTableBody.children.length === 0) {
                    const emptyRow = document.createElement('tr');
                    emptyRow.innerHTML = '<td colspan="5" class="text-center py-3">Нет данных для отображения</td>';
                    resultsTableBody.appendChild(emptyRow);
                }
            }
            
            // Показываем таблицу
            resultsTable.style.display = 'block';
            
            // Вызываем функцию фильтрации при первой загрузке
            filterAndDisplayData();
            
            // Добавляем обработчик изменения фильтра
            filterSelect.addEventListener('change', filterAndDisplayData);
        }
    });
</script>
{% endblock %}