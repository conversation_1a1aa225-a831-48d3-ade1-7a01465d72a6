/* Medical Mind - Стили приложения */

:root {
    --primary-color: #4e73df;
    --secondary-color: #2e59d9;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
    
    /* Переменные для светлой темы */
    --bg-color: #f8f9fc;
    --text-color: #5a5c69;
    --card-bg: #ffffff;
    --border-color: #e3e6f0;
    --footer-bg: #f8f9fc;
    --footer-text: #858796;
}

/* Темная тема */
body.dark-theme {
    --bg-color: #1e2130;
    --text-color: #e0e0e0;
    --card-bg: #2a2f45;
    --border-color: #3a3f58;
    --footer-bg: #1a1c28;
    --footer-text: #a0a0a0;
}

/* Общие стили */
body {
    background-color: var(--bg-color);
    color: var(--text-color);
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Стили для шапки */
.navbar-custom {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.navbar-scrolled {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

.navbar-brand {
    font-weight: 700;
    letter-spacing: 0.5px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-weight: bold;
    color: var(--primary-color);
}

/* Стили для карточек */
.card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
    transition: transform 0.2s, box-shadow 0.2s, background-color 0.3s;
    background-color: var(--card-bg);
}

.card-header {
    border-bottom: 1px solid var(--border-color);
    background-color: var(--card-bg);
}

.stat-card {
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
    opacity: 0;
    transform: translateY(20px);
}

.stat-card.show {
    opacity: 1;
    transform: translateY(0);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* Фоны для карточек статистики */
.bg-primary-dark, .bg-success-dark, .bg-warning-dark, .bg-info-dark, .bg-danger-dark {
    background-color: rgba(0, 0, 0, 0.15);
}

/* Стили для таблиц */
.table {
    color: var(--text-color);
}

.table th {
    border-top: none;
    font-weight: 700;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.03em;
}

.table-hover tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.05);
}

/* Стили для дропдаунов */
.dropdown-menu {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 8px;
    background-color: var(--card-bg);
}

.dropdown-item {
    color: var(--text-color);
}

.dropdown-item:hover {
    background-color: rgba(78, 115, 223, 0.1);
}

/* Стили для админ-панели */
.breadcrumb {
    background-color: transparent;
    padding: 0.5rem 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    font-size: 1.2rem;
    line-height: 1;
    vertical-align: middle;
}

/* Стили для кнопок */
.btn {
    border-radius: 6px;
    font-weight: 600;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Анимации и эффекты */
.animate-card {
    transition: all 0.3s ease;
}

.animate-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(78, 115, 223, 0.2);
}

/* Стили для бейджей */
.badge {
    font-weight: 600;
    padding: 0.35em 0.65em;
    border-radius: 6px;
}

/* Стили для футера */
.footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Стили для графиков на странице профиля */
.profile-chart {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.profile-chart.animate {
    opacity: 1;
    transform: translateY(0);
}

/* Стили для информации о пациенте */
.patient-info-row {
    font-size: 1.3rem;
    font-weight: 500;
    color: var(--text-color);
    border-left: 4px solid var(--primary-color);
    padding-left: 15px;
    background: var(--card-bg);
    border-radius: 8px;
    padding: 18px;
    margin-bottom: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.patient-info-row strong {
    font-size: 1.4rem;
    color: var(--primary-color);
}

.patient-info-row .fs-5 {
    font-size: 1.35rem !important;
    font-weight: 500;
}

/* Адаптивные стили */
@media (max-width: 767.98px) {
    .stat-card h2 {
        font-size: 1.5rem;
    }
    .user-avatar-large {
        width: 80px !important;
        height: 80px !important;
        font-size: 2rem !important;
    }
    .patient-info-row {
        font-size: 1.1rem;
        padding: 15px;
    }
    .patient-info-row strong {
        font-size: 1.2rem;
    }
    .patient-info-row .fs-5 {
        font-size: 1.15rem !important;
    }
}