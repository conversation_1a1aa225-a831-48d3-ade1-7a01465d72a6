#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Тестовый скрипт для проверки исправлений UI
"""

import re
import tkinter as tk
from tkinter import ttk

def test_bmi_calculation():
    """Тестирует функцию расчета ИМТ"""
    def calculate_bmi(body_type_str):
        try:
            if not body_type_str:
                return "Данные не указаны"
            
            # Извлекаем рост и вес из строки
            height_match = re.search(r'(\d+(?:\.\d+)?)\s*cm', body_type_str, re.IGNORECASE)
            weight_match = re.search(r'(\d+(?:\.\d+)?)\s*kg', body_type_str, re.IGNORECASE)
            
            if not height_match or not weight_match:
                return "Некорректные данные"
            
            height_cm = float(height_match.group(1))
            weight_kg = float(weight_match.group(1))
            
            # Переводим рост в метры
            height_m = height_cm / 100
            
            # Рассчитываем ИМТ
            bmi = weight_kg / (height_m ** 2)
            
            # Определяем интерпретацию ИМТ
            if bmi < 18.5:
                interpretation = "Недостаточный вес"
            elif 18.5 <= bmi < 25:
                interpretation = "Нормальный вес"
            elif 25 <= bmi < 30:
                interpretation = "Избыточный вес"
            else:
                interpretation = "Ожирение"
            
            return f"{bmi:.1f} ({interpretation})"
            
        except (ValueError, AttributeError) as e:
            print(f"Ошибка при расчете ИМТ: {e}")
            return "Ошибка расчета"
    
    # Тестовые случаи
    test_cases = [
        ("150cm, 57kg", "25.3 (Избыточный вес)"),
        ("170cm, 70kg", "24.2 (Нормальный вес)"),
        ("180cm, 90kg", "27.8 (Избыточный вес)"),
        ("160cm, 45kg", "17.6 (Недостаточный вес)"),
        ("175cm, 100kg", "32.7 (Ожирение)"),
    ]
    
    print("=== Тестирование расчета ИМТ ===")
    all_passed = True
    
    for input_data, expected in test_cases:
        result = calculate_bmi(input_data)
        passed = result == expected
        all_passed = all_passed and passed
        
        status = "✅ ПРОШЕЛ" if passed else "❌ НЕ ПРОШЕЛ"
        print(f"{status}: {input_data} -> {result}")
        if not passed:
            print(f"   Ожидалось: {expected}")
    
    print(f"\nОбщий результат: {'✅ Все тесты прошли' if all_passed else '❌ Есть ошибки'}")
    return all_passed

def test_date_formatting():
    """Тестирует очистку форматирования дат"""
    def clean_date(test_time):
        if test_time:
            # Извлекаем дату и время, игнорируя лишние символы
            date_match = re.search(r'(\d{1,2}\.\d{1,2}\.\d{4})', test_time)
            time_match = re.search(r'(\d{1,2}:\d{2})', test_time)

            if date_match:
                clean_date = date_match.group(1)
                if time_match:
                    clean_date += f" {time_match.group(1)}"
                return clean_date
        return test_time
    
    test_cases = [
        ("25.10.2024 x000D_ 09:14", "25.10.2024 09:14"),
        ("25.10.2024 r         09:14", "25.10.2024 09:14"),
        ("25.10.2024", "25.10.2024"),
        ("01.01.2024 12:30", "01.01.2024 12:30"),
    ]
    
    print("\n=== Тестирование очистки дат ===")
    all_passed = True
    
    for input_data, expected in test_cases:
        result = clean_date(input_data)
        passed = result == expected
        all_passed = all_passed and passed
        
        status = "✅ ПРОШЕЛ" if passed else "❌ НЕ ПРОШЕЛ"
        print(f"{status}: '{input_data}' -> '{result}'")
        if not passed:
            print(f"   Ожидалось: '{expected}'")
    
    print(f"\nОбщий результат: {'✅ Все тесты прошли' if all_passed else '❌ Есть ошибки'}")
    return all_passed

def test_ui_dialog():
    """Тестирует UI диалог ввода данных пациента"""
    print("\n=== Тестирование UI диалога ===")
    
    try:
        # Создаем простое тестовое окно
        root = tk.Tk()
        root.title("Тест UI исправлений")
        root.geometry("400x300")
        
        # Создаем фрейм для информации о пациенте
        patient_info_frame = ttk.LabelFrame(root, text="Информация о клиенте", padding=15)
        patient_info_frame.pack(fill='x', padx=10, pady=5)
        
        # Создаем метки с увеличенным шрифтом
        label1 = ttk.Label(patient_info_frame, font=('TkDefaultFont', 12, 'bold'))
        label1.pack(anchor='w', padx=5, pady=3)
        
        label2 = ttk.Label(patient_info_frame, font=('TkDefaultFont', 12, 'bold'))
        label2.pack(anchor='w', padx=5, pady=3)
        
        # Заполняем тестовыми данными
        label1.configure(text="ФИО: Наташа сестра     Пол: Женщины     Возраст: 56     Дата и время: 25.10.2024 09:14")
        label2.configure(text="Телосложение: 150cm, 57kg")
        
        # Добавляем кнопку закрытия
        ttk.Button(root, text="Закрыть", command=root.destroy).pack(pady=10)
        
        print("✅ UI диалог создан успешно")
        print("   - Увеличенный шрифт: 12pt, bold")
        print("   - Улучшенные отступы: padding=15")
        print("   - Очищенное форматирование даты")
        
        # Не запускаем mainloop для автоматического тестирования
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при создании UI: {e}")
        return False

if __name__ == "__main__":
    print("Запуск тестов исправлений UI Medical Mind\n")
    
    # Запускаем все тесты
    bmi_test = test_bmi_calculation()
    date_test = test_date_formatting()
    ui_test = test_ui_dialog()
    
    # Общий результат
    all_tests_passed = bmi_test and date_test and ui_test
    
    print(f"\n{'='*50}")
    print(f"ИТОГОВЫЙ РЕЗУЛЬТАТ: {'✅ ВСЕ ТЕСТЫ ПРОШЛИ' if all_tests_passed else '❌ ЕСТЬ ОШИБКИ'}")
    print(f"{'='*50}")
    
    if all_tests_passed:
        print("\n🎉 Все исправления работают корректно!")
        print("✅ BMI автоматически рассчитывается")
        print("✅ Шрифты увеличены для лучшей читаемости")
        print("✅ Даты очищены от лишних символов")
        print("✅ UI диалог создается без ошибок")
